import { io, Socket } from 'socket.io-client';
import { useAppStore } from './store';

// PRODUCTION-LEVEL: Connection pool and pre-warming system
interface ConnectionPool {
  socket: Socket | null;
  isConnected: boolean;
  isPreWarmed: boolean;
  connectionPromise: Promise<Socket> | null;
  reconnectAttempts: number;
  lastActivity: number;
  roomsJoined: Set<string>;
}

// Global connection pool
const connectionPool: ConnectionPool = {
  socket: null,
  isConnected: false,
  isPreWarmed: false,
  connectionPromise: null,
  reconnectAttempts: 0,
  lastActivity: Date.now(),
  roomsJoined: new Set()
};

// PERFORMANCE: Optimized constants for LAN environment
const MAX_RECONNECT_ATTEMPTS = Infinity;
const LAN_HEARTBEAT_INTERVAL = 2000; // IMMEDIATE: 2s for instant browser close detection
const CONNECTION_TIMEOUT = 2000; // Reduced from 5000ms to 2000ms for LAN
const RECONNECTION_DELAY = 100; // Reduced from 500ms to 100ms for LAN
const RECONNECTION_DELAY_MAX = 500; // Reduced from 2000ms to 500ms for LAN

// Connection health monitoring
let heartbeatInterval: NodeJS.Timeout | null = null;
let connectionHealthInterval: NodeJS.Timeout | null = null;

// LAN-OPTIMIZED: Event deduplication system (keep for real-time updates)
const processedEvents = new Set<string>();

// Track resource viewing and editing
let inactivityTimer: NodeJS.Timeout | null = null;
const INACTIVITY_TIMEOUT = 5 * 60 * 1000; // 5 minutes

// PRODUCTION-LEVEL: Pre-warm WebSocket connection for authenticated users only
export const preWarmConnection = (): Promise<Socket> => {
  console.log('🚀 PRE-WARMING: Starting WebSocket connection pre-warming...');

  // SECURITY FIX: Only pre-warm for authenticated users
  const currentUser = useAppStore.getState().currentUser;
  if (!currentUser) {
    console.log('🚀 PRE-WARMING: No authenticated user - skipping pre-warming to prevent auth errors');
    return Promise.reject(new Error('Pre-warming requires authenticated user'));
  }

  if (connectionPool.connectionPromise) {
    console.log('🚀 PRE-WARMING: Connection already in progress, returning existing promise');
    return connectionPool.connectionPromise;
  }

  connectionPool.connectionPromise = new Promise((resolve, reject) => {
    const socketUrl = '/';
    console.log('🚀 PRE-WARMING: Creating pre-warmed Socket.IO connection to:', socketUrl);

    const preWarmedSocket = io(socketUrl, {
      // PERFORMANCE: Optimized for immediate connection
      reconnection: true,
      reconnectionAttempts: Infinity,
      reconnectionDelay: RECONNECTION_DELAY,
      reconnectionDelayMax: RECONNECTION_DELAY_MAX,
      randomizationFactor: 0,
      timeout: CONNECTION_TIMEOUT,

      // PERFORMANCE: Optimized transport strategy
      transports: ['polling', 'websocket'],
      upgrade: true,
      rememberUpgrade: false,
      forceNew: false, // Allow reuse for pre-warming
      autoConnect: true,
      closeOnBeforeunload: false,
      withCredentials: true,
      multiplex: true,
      tryAllTransports: true
    });

    // Set up pre-warming event handlers
    preWarmedSocket.on('connect', () => {
      console.log('🚀 PRE-WARMING: Connection established successfully');
      connectionPool.socket = preWarmedSocket;
      connectionPool.isConnected = true;
      connectionPool.isPreWarmed = true;
      connectionPool.lastActivity = Date.now();

      // Start health monitoring immediately
      startConnectionHealthMonitoring();

      resolve(preWarmedSocket);
    });

    preWarmedSocket.on('connect_error', (error) => {
      console.error('🚀 PRE-WARMING: Connection failed:', error);
      connectionPool.connectionPromise = null;
      reject(error);
    });

    // Set timeout for pre-warming
    setTimeout(() => {
      if (!connectionPool.isConnected) {
        console.error('🚀 PRE-WARMING: Connection timeout');
        connectionPool.connectionPromise = null;
        reject(new Error('Pre-warming connection timeout'));
      }
    }, CONNECTION_TIMEOUT);
  });

  return connectionPool.connectionPromise;
};

// PRODUCTION-LEVEL: Start connection health monitoring
const startConnectionHealthMonitoring = () => {
  if (connectionHealthInterval) {
    clearInterval(connectionHealthInterval);
  }

  connectionHealthInterval = setInterval(() => {
    if (connectionPool.socket && connectionPool.isConnected) {
      // Send heartbeat
      connectionPool.socket.emit('heartbeat', { timestamp: Date.now() });
      connectionPool.lastActivity = Date.now();

      // IMMEDIATE: Check for stale connections (no activity for 5 seconds)
      const now = Date.now();
      if (now - connectionPool.lastActivity > 5000) {
        console.warn('🔍 IMMEDIATE: Stale connection detected, reconnecting...');
        connectionPool.socket.disconnect();
        connectionPool.socket.connect();
      }
    }
  }, LAN_HEARTBEAT_INTERVAL);
};

// PRODUCTION-LEVEL: Optimized WebSocket connection with pre-warming support
export const connectSocket = (): Socket => {
  console.log('🔌 OPTIMIZED CONNECTION: Starting production-level WebSocket connection...');

  // PERFORMANCE: Use pre-warmed connection if available
  if (connectionPool.isPreWarmed && connectionPool.socket && connectionPool.isConnected) {
    console.log('🚀 PERFORMANCE: Using pre-warmed connection - INSTANT CONNECTION!');

    // Set up event listeners if not already done
    if (!connectionPool.socket.hasListeners('connect')) {
      setupSocketListeners(connectionPool.socket);
    }

    // Join department room immediately for authenticated users
    const currentUser = useAppStore.getState().currentUser;
    if (currentUser) {
      joinDepartmentRoomImmediate(connectionPool.socket, currentUser);
    }

    return connectionPool.socket;
  }

  // FALLBACK: Create new connection if pre-warming failed or not available
  console.log('🔌 FALLBACK: Creating new optimized connection...');

  // Clear any existing connection in pool
  if (connectionPool.socket) {
    console.log('Closing existing connection pool socket before creating a new one');
    connectionPool.socket.disconnect();
    connectionPool.socket = null;
    connectionPool.isConnected = false;
    connectionPool.isPreWarmed = false;
  }

  // PRODUCTION: Use same-origin connection for single-server deployment
  const socketUrl = '/';
  console.log('🔌 Creating optimized Socket.IO connection to:', socketUrl);

  const newSocket = io(socketUrl, {
    // PERFORMANCE: Optimized for LAN environment
    reconnection: true,
    reconnectionAttempts: Infinity,
    reconnectionDelay: RECONNECTION_DELAY,
    reconnectionDelayMax: RECONNECTION_DELAY_MAX,
    randomizationFactor: 0,
    timeout: CONNECTION_TIMEOUT,

    // PERFORMANCE: Optimized transport strategy
    transports: ['polling', 'websocket'],
    upgrade: true,
    rememberUpgrade: false,
    forceNew: true,
    autoConnect: true,
    closeOnBeforeunload: false,
    withCredentials: true,
    multiplex: true,
    tryAllTransports: true
  });

  // Update connection pool
  connectionPool.socket = socket;
  connectionPool.isConnected = false; // Will be set to true on connect event

  console.log('🔌 OPTIMIZED: Socket.IO connection config:', {
    url: socketUrl,
    transports: ['polling', 'websocket'],
    timeout: CONNECTION_TIMEOUT,
    reconnectionDelay: RECONNECTION_DELAY,
    optimized: true
  });

  // Get current user for debugging
  const currentUser = useAppStore.getState().currentUser;
  if (currentUser) {
    console.log(`🔌 Connecting WebSocket for ${currentUser.name} (${currentUser.department})`);
    if (currentUser.department === 'AUDIT') {
      console.log('🔍 AUDIT user detected - enabling enhanced debugging');
      console.log('🔍 AUDIT WebSocket URL:', socketUrl);
    }
  }

  console.log('Socket connection initialized with token and path configuration');

  // Store in connection pool
  connectionPool.socket = newSocket;
  connectionPool.isConnected = false; // Will be set to true on connect event
  connectionPool.isPreWarmed = false;

  // Setup event listeners
  setupSocketListeners(newSocket);

  // LAN-OPTIMIZED: Simple reconnection handling
  newSocket.on('reconnect_attempt', (attemptNumber) => {
    console.log(`LAN reconnection attempt ${attemptNumber}...`);
  });

  newSocket.on('reconnect', (attemptNumber) => {
    console.log(`LAN reconnected after ${attemptNumber} attempts`);
    connectionPool.isConnected = true;

    // Rejoin department room immediately
    const store = useAppStore.getState();
    if (store.user?.department) {
      newSocket.emit('join_department', {
        department: store.user.department,
        userId: store.user.id,
        userName: store.user.name
      });
    }
  });

  newSocket.on('reconnect_error', (error) => {
    console.log('LAN reconnection error (will retry):', error.message);
  });

  // LAN-OPTIMIZED: Simple heartbeat for local server
  heartbeatInterval = setInterval(() => {
    if (newSocket?.connected) {
      newSocket.emit('heartbeat');
    }
  }, LAN_HEARTBEAT_INTERVAL);

  // LAN-OPTIMIZED: Simple error handling
  newSocket.on('connect_error', (error) => {
    console.log('LAN connection error (will retry):', error.message);
  });

  newSocket.on('disconnect', (reason) => {
    console.log('LAN disconnected:', reason);
    connectionPool.isConnected = false;
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }
  });

  return newSocket;
};

// PRODUCTION-LEVEL: Immediate department room joining for pre-warmed connections
const joinDepartmentRoomImmediate = (socket: Socket, user: any) => {
  console.log('🚀 IMMEDIATE: Joining department room for pre-warmed connection...');

  const departmentRoom = `department:${user.department}`;

  // Check if already joined to avoid duplicate joins
  if (connectionPool.roomsJoined.has(departmentRoom)) {
    console.log('🚀 IMMEDIATE: Already joined department room, skipping...');
    return;
  }

  // Join department room immediately
  socket.emit('join_department', {
    department: user.department,
    userId: user.id,
    userName: user.name
  });

  // Mark room as joined
  connectionPool.roomsJoined.add(departmentRoom);

  console.log(`🚀 IMMEDIATE: Joined ${departmentRoom} room instantly`);

  // For AUDIT users, also join admin-users room
  if (user.department === 'AUDIT') {
    const adminRoom = 'admin-users';
    if (!connectionPool.roomsJoined.has(adminRoom)) {
      socket.emit('join_room', { room: adminRoom });
      connectionPool.roomsJoined.add(adminRoom);
      console.log('🚀 IMMEDIATE: AUDIT user joined admin-users room instantly');
    }
  }
};

// PRODUCTION-LEVEL: Get socket with automatic pre-warming fallback
export const getSocket = (): Socket | null => {
  // Return connection pool socket if available and connected
  if (connectionPool.socket && connectionPool.isConnected) {
    return connectionPool.socket;
  }

  // Return connection pool socket even if not marked as connected (for fallback)
  if (connectionPool.socket) {
    return connectionPool.socket;
  }

  // Try to pre-warm connection if none available (only for authenticated users)
  const currentUser = useAppStore.getState().currentUser;
  if (currentUser) {
    console.log('🚀 AUTO-PREWARM: No connection available, attempting pre-warming for authenticated user...');
    preWarmConnection().catch(error => {
      console.error('🚀 AUTO-PREWARM: Failed to pre-warm connection:', error);
    });
  } else {
    console.log('🚀 AUTO-PREWARM: No connection available, but user not authenticated - skipping pre-warming');
  }

  return null;
};

// PRODUCTION-LEVEL: Enhanced disconnect with connection pool cleanup
export const disconnectSocket = (): void => {
  console.log('🔌 DISCONNECT: Cleaning up all connections...');

  // Clear health monitoring
  if (connectionHealthInterval) {
    clearInterval(connectionHealthInterval);
    connectionHealthInterval = null;
  }

  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }

  // Disconnect main socket
  const currentSocket = getSocket();
  if (currentSocket) {
    currentSocket.disconnect();
  }

  // Clean up connection pool
  if (connectionPool.socket) {
    connectionPool.socket.disconnect();
    connectionPool.socket = null;
  }

  // Reset connection pool state
  connectionPool.isConnected = false;
  connectionPool.isPreWarmed = false;
  connectionPool.connectionPromise = null;
  connectionPool.reconnectAttempts = 0;
  connectionPool.roomsJoined.clear();

  console.log('🔌 DISCONNECT: All connections cleaned up');
};

// PRODUCTION-LEVEL: Optimized socket event listeners with parallel processing
const setupSocketListeners = (socket: Socket): void => {
  // Handle connection events with performance optimization
  socket.on('connect', () => {
    console.log('🚀 OPTIMIZED: WebSocket connected! Socket ID:', socket.id);

    // Update connection pool state
    connectionPool.isConnected = true;
    connectionPool.reconnectAttempts = 0;
    connectionPool.lastActivity = Date.now();

    // PERFORMANCE: Start health monitoring immediately
    if (!connectionHealthInterval) {
      startConnectionHealthMonitoring();
    }

    // AUTOMATED: Trigger offline sync when WebSocket reconnects
    const { isOnline, syncOfflineOperations } = useAppStore.getState();
    if (isOnline) {
      console.log('🔄 AUTOMATED: WebSocket reconnected - triggering sync');
      setTimeout(() => {
        syncOfflineOperations().catch(error => {
          console.error('❌ AUTOMATED: WebSocket reconnection sync failed:', error);
        });
      }, 1000); // Small delay to ensure connection is stable
    }

    // PERFORMANCE: Parallel room joining for authenticated users
    const currentUser = useAppStore.getState().currentUser;
    if (currentUser) {
      console.log(`🚀 PARALLEL: Processing room joins for ${currentUser.name} (${currentUser.department})`);

      // Use parallel processing for room joins
      const roomJoinPromises = [];

      // Primary department room
      const departmentRoom = `department:${currentUser.department}`;
      if (!connectionPool.roomsJoined.has(departmentRoom)) {
        const departmentJoin = new Promise<void>((resolve) => {
          socket.emit('join_department', {
            department: currentUser.department,
            userId: currentUser.id,
            userName: currentUser.name
          });
          connectionPool.roomsJoined.add(departmentRoom);
          console.log(`🚀 PARALLEL: Joined ${departmentRoom} room`);
          resolve();
        });
        roomJoinPromises.push(departmentJoin);
      }

      // AUDIT users: admin-users room
      if (currentUser.department === 'AUDIT') {
        const adminRoom = 'admin-users';
        if (!connectionPool.roomsJoined.has(adminRoom)) {
          const adminJoin = new Promise<void>((resolve) => {
            socket.emit('join_room', { room: adminRoom });
            connectionPool.roomsJoined.add(adminRoom);
            console.log('🚀 PARALLEL: AUDIT user joined admin-users room');
            resolve();
          });
          roomJoinPromises.push(adminJoin);
        }
      }

      // Execute all room joins in parallel
      Promise.all(roomJoinPromises).then(() => {
        console.log('🚀 PARALLEL: All room joins completed');

        // PERFORMANCE: Parallel data fetching
        const dataFetchPromises = [
          useAppStore.getState().fetchVouchers(),
          useAppStore.getState().fetchAllUsers()
        ];

        Promise.all(dataFetchPromises).then(() => {
          console.log('🚀 PARALLEL: All data fetching completed');
        }).catch(error => {
          console.error('🚀 PARALLEL: Data fetching error:', error);
        });
      });

      // Show success notification
      if (window.toast) {
        window.toast({
          title: "Connected",
          description: `Ready for real-time notifications`,
          variant: "default",
        });
      }
    } else {
      console.log('⚠️ OPTIMIZED: No user available, will retry on authentication');
    }
  });

  // CRITICAL FIX: Listen for department room join confirmation
  socket.on('joined_department', (data) => {
    console.log('✅ Successfully joined department room:', data);
    if (data.department === 'AUDIT') {
      console.log('🔍 AUDIT user successfully joined department room - ready for real-time notifications');
    }
  });

  socket.on('disconnect', (reason) => {
    console.log(`LAN server disconnected: ${reason}`);
    isConnected = false;

    // LAN-OPTIMIZED: Simple status notification (no dramatic error messages)
    const currentUser = useAppStore.getState().currentUser;
    if (currentUser && window.toast) {
      window.toast({
        title: "Server Connecting...",
        description: `Reconnecting to local server...`,
        variant: "default",
      });
    }

    // LAN-OPTIMIZED: Immediate reconnection attempt for local server
    if (reason === 'io server disconnect' || reason === 'io client disconnect') {
      console.log('Reconnecting to LAN server...');
      socket.connect();
    }

    // Stop heartbeat
    stopHeartbeat();
  });

  socket.on('reconnect', (attemptNumber) => {
    console.log(`LAN server reconnected after ${attemptNumber} attempts`);
    isConnected = true;

    // LAN-OPTIMIZED: Simple reconnection notification
    const currentUser = useAppStore.getState().currentUser;
    if (currentUser && window.toast) {
      window.toast({
        title: "Connected",
        description: `Connected to local server`,
        variant: "default",
      });

      // LAN-OPTIMIZED: No data refresh needed - local data is never stale
      // Real-time updates will resume automatically via socket events
    }

    // Restart heartbeat
    startHeartbeat();
  });

  socket.on('reconnect_attempt', (attemptNumber) => {
    reconnectAttempts = attemptNumber;
    console.log(`Attempting to reconnect to WebSocket server (attempt ${attemptNumber} of ${MAX_RECONNECT_ATTEMPTS})`);

    // Show toast notification for reconnection attempt if it's been a while
    if (attemptNumber % 5 === 0) { // Show every 5 attempts
      const currentUser = useAppStore.getState().currentUser;
      if (currentUser && window.toast) {
        window.toast({
          title: "Reconnecting",
          description: `Attempting to reconnect to server (${attemptNumber}/${MAX_RECONNECT_ATTEMPTS})`,
          variant: "default",
        });
      }
    }
  });

  socket.on('reconnect_error', (error) => {
    console.error('Error reconnecting to WebSocket server:', error);
  });

  // LAN-OPTIMIZED: This should rarely happen with infinite reconnection attempts
  socket.on('reconnect_failed', () => {
    console.log('LAN reconnection stopped (this should not happen with infinite attempts)');

    // This event should not occur with infinite reconnection attempts
    // But if it does, it means there's a serious issue with the local server
    const currentUser = useAppStore.getState().currentUser;
    if (currentUser && window.toast) {
      window.toast({
        title: "Server Down",
        description: `Local server appears to be down. Please contact IT support.`,
        variant: "destructive",
      });
    }
  });

  // LAN-OPTIMIZED: Simple error handling for local server
  socket.on('connect_error', (error) => {
    console.log('LAN connection error:', error.message);

    // LAN-OPTIMIZED: Simple error counting - no complex recovery strategies needed
    const errorCount = (socket as any)._errorCount || 0;
    (socket as any)._errorCount = errorCount + 1;

    // Show error only after several attempts (server likely down for maintenance)
    if (errorCount > 5) {
      if (window.toast) {
        window.toast({
          title: "Server Unavailable",
          description: `Cannot connect to local server. Please check if the server is running.`,
          variant: "destructive",
        });
      }
      // Reset counter to avoid spam
      (socket as any)._errorCount = 0;
    }
  });

  // Handle lock updates
  socket.on('locks_update', (data) => {
    console.log('Received locks update:', data);
    // Update locks in store
    const store = useAppStore.getState();
    if (store.updateResourceLocks) {
      store.updateResourceLocks(data.locks);
    }
  });

  socket.on('lock_update', (data) => {
    console.log('Received lock update:', data);
    // Update lock in store
    const store = useAppStore.getState();
    if (store.updateResourceLock) {
      store.updateResourceLock(data.key, data.isLocked, data.userId);
    }
  });

  // Handle state updates
  socket.on('state_update', (data) => {
    console.log('Received state update:', data);

    // Update store based on update type
    const store = useAppStore.getState();

    // ARCHITECTURAL FIX: Robust event handling with deduplication
    const eventId = data.eventId;
    if (eventId && processedEvents.has(eventId)) {
      console.log('🔄 WEBSOCKET: Duplicate event ignored:', eventId);
      return;
    }

    if (eventId) {
      processedEvents.add(eventId);
      // Clean up old events (keep last 100)
      if (processedEvents.size > 100) {
        const oldEvents = Array.from(processedEvents).slice(0, 50);
        oldEvents.forEach(id => processedEvents.delete(id));
      }
    }

    switch (data.type) {
      case 'VOUCHER_UPDATE':
      case 'UPDATE':
        if (store.updateVoucherFromWebSocket && data.voucher) {
          // CRITICAL FIX: Use WebSocket update method to prevent infinite loop
          // updateVoucher() calls API which triggers another broadcast, causing infinite loop
          store.updateVoucherFromWebSocket(data.voucher.id, data.voucher);
        }
        break;

      case 'VOUCHER_CREATE':
      case 'CREATED':
        if (store.addVoucherToStore && data.voucher) {

          // ARCHITECTURAL FIX: Comprehensive data normalization
          const normalizedVoucher = {
            ...data.voucher,
            voucherId: data.voucher.voucherId || data.voucher.voucher_id,
            sentToAudit: Boolean(data.voucher.sentToAudit || data.voucher.sent_to_audit),
            deleted: Boolean(data.voucher.deleted),
            createdBy: data.voucher.createdBy || data.voucher.created_by,
            dispatchedBy: data.voucher.dispatchedBy || data.voucher.dispatched_by || '',
            isReturned: Boolean(data.voucher.isReturned || data.voucher.is_returned),
            pendingReturn: Boolean(data.voucher.pendingReturn || data.voucher.pending_return),
            dispatched: Boolean(data.voucher.dispatched)
          };

          // Use the dedicated function for adding existing vouchers
          store.addVoucherToStore(normalizedVoucher);

          // PRODUCTION FIX: Immediate UI update with state change detection
          const currentState = store.getState();
          console.log('🔄 WEBSOCKET: Voucher added - Current count:', currentState.vouchers.length);

          // Force component re-render by triggering state change
          store.setState({ lastUpdate: Date.now() });

          // CRITICAL FIX: Dispatch custom event for Department Voucher Hub real-time updates
          window.dispatchEvent(new CustomEvent('voucherUpdated', {
            detail: {
              voucher: normalizedVoucher,
              type: 'created',
              statusChanged: true,
              forceStatusRefresh: true
            }
          }));

          // RETURNED VOUCHER BACKSTAGE REFRESH: Dispatch specific event for returned voucher copies
          if (normalizedVoucher.isReturnedCopy || normalizedVoucher.is_returned_copy ||
              normalizedVoucher.workflowState === 'FINANCE_RETURNED_COPY') {
            console.log('🔄 RETURNED VOUCHER BACKSTAGE REFRESH: Dispatching returnedVoucherCreated event', normalizedVoucher.voucherId);
            window.dispatchEvent(new CustomEvent('returnedVoucherCreated', {
              detail: { voucher: normalizedVoucher, type: 'returned_copy_created' }
            }));
          }
        }
        break;

      case 'VOUCHER_DELETE':
        if (store.deleteVoucher && data.voucherId) {
          store.deleteVoucher(data.voucherId);
        }
        break;

      case 'BATCH_UPDATE':
        if (store.updateVoucherBatch && data.batch) {
          store.updateVoucherBatch(data.batch.id, data.batch);
        }
        break;

      case 'BATCH_CREATE':
        if (store.createVoucherBatch && data.batch) {
          // Add batch to store
          store.createVoucherBatch(
            data.batch.department,
            data.batch.voucherIds,
            data.batch.sentBy
          );
        }
        break;

      case 'NOTIFICATION_CREATE':
        if (store.addNotification && data.notification) {
          store.addNotification(data.notification);
        }
        break;

      case 'USER_UPDATE':
        if (store.updateUser && data.user) {
          store.updateUser(data.user.id, data.user);
        }
        break;

      default:
        console.log('Unknown state update type:', data.type);
    }
  });

  // Handle user updates
  socket.on('user_update', (data) => {
    console.log('Received user update:', data);
    const store = useAppStore.getState();

    switch (data.type) {
      case 'created':
        if (store.addUser && data.user) {
          store.addUser(data.user);
          // Login page will handle its own updates via WebSocket
        }
        break;

      case 'updated':
        if (store.updateUser && data.user) {
          store.updateUser(data.user.id, data.user);
        }
        break;

      case 'deleted':
        if (store.deleteUser && data.user) {
          store.deleteUser(data.user.id);
          // Login page will handle its own updates via WebSocket
        }
        break;

      case 'approved':
        if (store.addUser && data.user) {
          store.addUser(data.user);
          // Login page will handle its own updates via WebSocket
        }
        break;

      default:
        console.log('Unknown user update type:', data.type);
    }
  });

  // Handle registration updates
  socket.on('registration_update', (data) => {
    console.log('Received registration update:', data);
    const store = useAppStore.getState();

    switch (data.type) {
      case 'created':
        if (data.registration) {
          console.log('New registration received:', data.registration);

          // Directly add the registration to the store
          const pendingRegistrations = store.pendingRegistrations || [];
          const exists = pendingRegistrations.some(reg => reg.id === data.registration.id);

          if (!exists) {
            store.setPendingRegistrations([...pendingRegistrations, data.registration]);
            console.log('Added new registration to store');
          }

          // Also fetch from server to ensure consistency
          store.fetchPendingRegistrations().then(() => {
            console.log('Fetched pending registrations after new registration');
          });
        }
        break;

      case 'approved':
        if (data.registration) {
          console.log('Registration approved:', data.registration);

          // Remove the registration from pending list
          const updatedRegistrations = (store.pendingRegistrations || []).filter(
            reg => reg.id !== data.registration.id
          );
          store.setPendingRegistrations(updatedRegistrations);

          // Also fetch from server to ensure consistency
          store.fetchPendingRegistrations().then(() => {
            console.log('Fetched pending registrations after approval');
          });
        }
        break;

      case 'rejected':
        if (data.registration) {
          console.log('Registration rejected:', data.registration);

          // Remove the registration from pending list
          const updatedRegistrations = (store.pendingRegistrations || []).filter(
            reg => reg.id !== data.registration.id
          );
          store.setPendingRegistrations(updatedRegistrations);

          // Also fetch from server to ensure consistency
          store.fetchPendingRegistrations().then(() => {
            console.log('Fetched pending registrations after rejection');
          });
        }
        break;

      default:
        console.log('Unknown registration update type:', data.type);
    }
  });

  // Handle voucher updates
  socket.on('voucher_update', (data) => {
    const store = useAppStore.getState();
    const currentUser = store.currentUser;

    // Track processed events to avoid duplicates
    const processedEvents = window._processedSocketEvents = window._processedSocketEvents || new Set();

    // Check if we've already processed this event (if it has an eventId)
    if (data.eventId && processedEvents.has(data.eventId)) {
      return;
    }

    // Mark this event as processed if it has an eventId
    if (data.eventId) {
      processedEvents.add(data.eventId);

      // Clean up old events (keep only the last 100)
      if (processedEvents.size > 100) {
        const toRemove = Array.from(processedEvents).slice(0, processedEvents.size - 100);
        toRemove.forEach(id => processedEvents.delete(id));
      }
    }

    // Skip processing if no current user
    if (!currentUser) {
      return;
    }

    // Check if this voucher belongs to the current user's department or if user is AUDIT/SYSTEM ADMIN
    const voucherDepartment = data.voucher?.department;
    const isRelevantForUser =
      currentUser.department === 'AUDIT' ||
      currentUser.department === 'SYSTEM ADMIN' ||
      voucherDepartment === currentUser.department;

    if (!isRelevantForUser) {
      return;
    }

    switch (data.type) {
      case 'created':
        if (data.voucher) {
          // PRODUCTION FIX: Comprehensive duplicate checking
          const existingVoucher = store.vouchers.find(v =>
            v.id === data.voucher.id ||
            v.voucherId === data.voucher.voucher_id ||
            (v.voucher_id && v.voucher_id === data.voucher.voucher_id) ||
            (v.voucherId === data.voucher.voucherId)
          );

          if (!existingVoucher) {
            // CRITICAL FIX: Format voucher for client compatibility
            const clientVoucher = {
              ...data.voucher,
              // Ensure all required client fields are present
              voucherId: data.voucher.voucher_id || data.voucher.voucherId,
              createdBy: data.voucher.created_by || data.voucher.createdBy || '',
              dispatchedBy: data.voucher.dispatched_by || data.voucher.dispatchedBy || '',
              sentToAudit: data.voucher.sent_to_audit !== undefined ? Boolean(data.voucher.sent_to_audit) : false,
              isReturned: data.voucher.is_returned !== undefined ? Boolean(data.voucher.is_returned) : false,
              pendingReturn: data.voucher.pending_return !== undefined ? Boolean(data.voucher.pending_return) : false,
              dispatched: data.voucher.dispatched !== undefined ? Boolean(data.voucher.dispatched) : false,
              lastUpdated: new Date().toISOString()
            };

            // Add voucher directly to store (don't call addVoucher to avoid API call)
            const newVouchers = [...store.vouchers, clientVoucher];
            useAppStore.setState({ vouchers: newVouchers });

            console.log(`✅ Added new voucher to store: ${clientVoucher.voucherId} (${clientVoucher.status})`);

            // Show toast notification for new voucher
            if (window.toast) {
              window.toast({
                title: "New Voucher Created",
                description: `Voucher ${clientVoucher.voucherId} has been created`,
                variant: "default",
              });
            }
          }
        }
        break;

      case 'returned':
        // RETURN VOUCHER FIX: Handle returned voucher real-time updates
        if (store.updateVoucherFromWebSocket && data.voucher) {
          console.log('RETURN VOUCHER: Processing returned voucher update for real-time removal:', data.voucher.voucher_id);

          // Convert server voucher format to client format with ALL required fields
          const clientVoucher = {
            ...data.voucher,
            voucherId: data.voucher.voucher_id || data.voucher.voucherId,
            dispatchedBy: data.voucher.dispatched_by || data.voucher.dispatchedBy || '',
            createdBy: data.voucher.created_by || data.voucher.createdBy || '',
            returnedBy: data.voucher.returned_by || data.voucher.returnedBy,
            returnComment: data.voucher.return_reason || data.voucher.returnComment,
            comment: data.voucher.return_reason || data.voucher.comment,
            workflow_state: data.voucher.workflow_state,
            status: data.voucher.status,
            lastUpdated: new Date().toISOString(),
          };

          // Update voucher in store for immediate tab movement
          store.updateVoucherFromWebSocket(data.voucher.id, clientVoucher);

          // Show notification
          if (window.toast) {
            window.toast({
              title: "Voucher Returned",
              description: `${data.voucher.voucher_id} has been returned to Finance`,
              variant: "default",
            });
          }
        }
        break;

      case 'updated':
        if (store.updateVoucherFromWebSocket && data.voucher) {
          console.log('PRODUCTION FIX: Processing voucher update for real-time tab movement:', data.voucher.voucher_id);

          // CRITICAL FIX: Convert server voucher format to client format for proper tab movement
          const clientVoucher = {
            ...data.voucher,
            // Ensure all required client fields are present
            voucherId: data.voucher.voucher_id || data.voucher.voucherId,
            dispatchedBy: data.voucher.dispatched_by || data.voucher.dispatchedBy || '',
            createdBy: data.voucher.created_by || data.voucher.createdBy || '',
            lastUpdated: new Date().toISOString(),

            // CRITICAL: Map server database fields to client fields for proper tab filtering
            sentToAudit: data.voucher.sent_to_audit !== undefined ? Boolean(data.voucher.sent_to_audit) : data.voucher.sentToAudit,
            isReturned: data.voucher.is_returned !== undefined ? Boolean(data.voucher.is_returned) : data.voucher.isReturned,
            pendingReturn: data.voucher.pending_return !== undefined ? Boolean(data.voucher.pending_return) : data.voucher.pendingReturn,
            dispatched: data.voucher.dispatched !== undefined ? Boolean(data.voucher.dispatched) : false,
            workStarted: data.voucher.work_started !== undefined ? Boolean(data.voucher.work_started) : data.voucher.workStarted,
            auditDispatchTime: data.voucher.audit_dispatch_time || data.voucher.auditDispatchTime,
            auditDispatchedBy: data.voucher.audit_dispatched_by || data.voucher.auditDispatchedBy,

            // Preserve all other fields
            id: data.voucher.id,
            date: data.voucher.date,
            claimant: data.voucher.claimant,
            description: data.voucher.description,
            amount: data.voucher.amount,
            currency: data.voucher.currency,
            department: data.voucher.department,
            status: data.voucher.status
          };

          // PRODUCTION FIX: Update voucher in store for immediate tab movement
          store.updateVoucherFromWebSocket(data.voucher.id, clientVoucher);

          // Show notification for status changes only
          if (data.voucher.status && window.toast) {
            window.toast({
              title: "Voucher Status Updated",
              description: `${data.voucher.voucher_id} is now ${data.voucher.status}`,
              variant: "default",
            });
          }

          // CRITICAL FIX: Dispatch custom event for Department Voucher Hub real-time updates
          // Enhanced with status update information for real-time status column updates
          window.dispatchEvent(new CustomEvent('voucherUpdated', {
            detail: {
              voucher: clientVoucher,
              type: 'updated',
              statusChanged: true,
              forceStatusRefresh: true
            }
          }));

          console.log(`🔄 REAL-TIME STATUS: Dispatched status update event for voucher ${clientVoucher.voucherId}`);
        }
        break;

      case 'status_changed':
        // CRITICAL FIX: Handle dedicated status change events for real-time status column updates
        if (store.updateVoucherFromWebSocket && data.voucher) {
          console.log(`🔄 REAL-TIME STATUS: Processing status change for voucher:`, data.voucher.voucher_id || data.voucher.id);
          console.log(`🔄 REAL-TIME STATUS: ${data.voucher.previousStatus} → ${data.voucher.newStatus}`);

          // Use the same voucher normalization as 'updated' case
          const clientVoucher = {
            ...data.voucher,
            voucherId: data.voucher.voucher_id || data.voucher.voucherId,
            dispatchedBy: data.voucher.dispatched_by || data.voucher.dispatchedBy || '',
            createdBy: data.voucher.created_by || data.voucher.createdBy || '',
            lastUpdated: new Date().toISOString(),
            sentToAudit: data.voucher.sent_to_audit !== undefined ? Boolean(data.voucher.sent_to_audit) : data.voucher.sentToAudit,
            isReturned: data.voucher.is_returned !== undefined ? Boolean(data.voucher.is_returned) : data.voucher.isReturned,
            pendingReturn: data.voucher.pending_return !== undefined ? Boolean(data.voucher.pending_return) : data.voucher.pendingReturn,
            dispatched: data.voucher.dispatched !== undefined ? Boolean(data.voucher.dispatched) : false,
            workStarted: data.voucher.work_started !== undefined ? Boolean(data.voucher.work_started) : data.voucher.workStarted,
            auditDispatchTime: data.voucher.audit_dispatch_time || data.voucher.auditDispatchTime,
            auditDispatchedBy: data.voucher.audit_dispatched_by || data.voucher.auditDispatchedBy,
            id: data.voucher.id,
            date: data.voucher.date,
            claimant: data.voucher.claimant,
            description: data.voucher.description,
            amount: data.voucher.amount,
            currency: data.voucher.currency,
            department: data.voucher.department,
            status: data.voucher.status
          };

          // Update voucher in store
          store.updateVoucherFromWebSocket(data.voucher.id, clientVoucher);

          // Show status change notification
          if (data.voucher.status && window.toast) {
            window.toast({
              title: "Voucher Status Changed",
              description: `${data.voucher.voucher_id} status: ${data.voucher.previousStatus} → ${data.voucher.newStatus}`,
              variant: "default",
            });
          }

          // CRITICAL FIX: Dispatch enhanced event for real-time status column updates
          window.dispatchEvent(new CustomEvent('voucherUpdated', {
            detail: {
              voucher: clientVoucher,
              type: 'status_changed',
              statusChanged: true,
              forceStatusRefresh: true,
              previousStatus: data.voucher.previousStatus,
              newStatus: data.voucher.newStatus,
              statusChangeType: data.voucher.statusChangeType
            }
          }));

          console.log(`🔄 REAL-TIME STATUS: Dispatched status_changed event for voucher ${clientVoucher.voucherId}`);
        }
        break;

      case 'deleted':
        if (store.deleteVoucher && data.voucher) {
          console.log('Deleting voucher:', data.voucher.voucher_id || data.voucher.id);
          store.deleteVoucher(data.voucher.id);

          // Show toast notification for deleted voucher
          if (window.toast) {
            window.toast({
              title: "Voucher Deleted",
              description: `Voucher ${data.voucher.voucher_id || data.voucher.id} has been deleted`,
              variant: "default",
            });
          }

          // REMOVED: Force refresh to prevent flickering - WebSocket update is sufficient
          // The real-time update already provides the latest data
        }
        break;

      default:
        console.log('Unknown voucher update type:', data.type);
    }
  });

  // Handle batch updates
  socket.on('batch_update', (data) => {
    console.log('🔔 Received batch update:', data);
    const store = useAppStore.getState();

    switch (data.type) {
      case 'created':
        console.log('🔔 New batch created, refreshing batches...');
        // Refresh batches to get the latest data
        if (store.fetchBatches) {
          store.fetchBatches().then(() => {
            console.log('✅ Batches refreshed after batch creation');
          }).catch((error) => {
            console.error('❌ Failed to refresh batches after creation:', error);
          });
        }
        break;

      case 'updated':
        console.log('🔔 Batch updated, refreshing batches...');
        if (store.fetchBatches) {
          store.fetchBatches().then(() => {
            console.log('✅ Batches refreshed after batch update');
          }).catch((error) => {
            console.error('❌ Failed to refresh batches after update:', error);
          });
        }
        break;

      case 'deleted':
        console.log('🔔 Batch deleted, refreshing batches...');
        if (store.fetchBatches) {
          store.fetchBatches().then(() => {
            console.log('✅ Batches refreshed after batch deletion');
          }).catch((error) => {
            console.error('❌ Failed to refresh batches after deletion:', error);
          });
        }
        break;

      default:
        console.log('Unknown batch update type:', data.type);
    }
  });

  // Handle notification updates
  socket.on('notification_update', (data) => {
    console.log('🔔 Received notification update:', data);
    const store = useAppStore.getState();
    const currentUser = store.currentUser;

    switch (data.type) {
      case 'created':
        if (store.addNotification && data.notification) {
          // Check if this notification is for the current user's department
          const isForCurrentUser = currentUser &&
            (data.notification.user_id === currentUser.department ||
             data.notification.user_id === currentUser.id);

          if (isForCurrentUser) {
            console.log('🔔 Adding notification for current user:', data.notification);
            store.addNotification({
              id: data.notification.id,
              userId: data.notification.user_id,
              message: data.notification.message,
              isRead: data.notification.is_read || false,
              timestamp: data.notification.timestamp,
              voucherId: data.notification.voucher_id,
              batchId: data.notification.batch_id,
              type: data.notification.type,
              fromAudit: data.notification.from_audit || false
            });

            // Show toast notification for new batch notifications
            if (data.notification.type === 'NEW_BATCH' && window.toast) {
              window.toast({
                title: "New Batch Received",
                description: data.notification.message,
                variant: "default",
              });
            }
          } else {
            console.log('🔔 Notification not for current user, ignoring');
          }
        }
        break;

      case 'updated':
        if (store.updateNotification && data.notification) {
          store.updateNotification(data.notification.id, data.notification);
        }
        break;

      case 'deleted':
        if (store.deleteNotification && data.notification) {
          store.deleteNotification(data.notification.id);
        }
        break;

      case 'BATCH_CREATED':
        // Handle batch creation notifications for returned vouchers
        console.log('🔔 Batch created notification:', data.notification);
        if (window.toast && data.notification) {
          window.toast({
            title: "New Batch Created",
            description: data.notification.message || `New batch created for ${data.notification.department}`,
            variant: "default",
          });
        }
        break;

      default:
        console.log('Unknown notification update type:', data.type);
    }
  });

  // Handle generic data updates
  socket.on('data_update', (data) => {
    console.log(`Received ${data.actionType} for ${data.entityType}:`, data.data);
    const store = useAppStore.getState();

    // Handle different entity types
    switch (data.entityType) {
      case 'voucher':
        handleVoucherUpdate(store, data.actionType, data.data);
        break;

      case 'batch':
        handleBatchUpdate(store, data.actionType, data.data);
        break;

      case 'user':
        handleUserUpdate(store, data.actionType, data.data);
        break;

      case 'notification':
        handleNotificationUpdate(store, data.actionType, data.data);
        break;

      case 'registration':
        handleRegistrationUpdate(store, data.actionType, data.data);
        break;

      default:
        console.log('Unknown entity type:', data.entityType);
    }
  });

  // Handle heartbeat acknowledgment
  socket.on('heartbeat_ack', (data) => {
    console.log('Heartbeat acknowledged:', data);
  });

  // Handle resource viewers updates
  socket.on('resource_viewers', (data) => {
    console.log('Received resource viewers update:', data);
    const store = useAppStore.getState();

    if (store.updateResourceViewers) {
      store.updateResourceViewers(data.resourceKey, data.viewers, data.viewerCount);
    }
  });

  // Handle force refresh
  socket.on('force_refresh', (data) => {
    console.log('Received force refresh:', data);
    const store = useAppStore.getState();
    const currentUser = store.currentUser;

    if (!currentUser) {
      console.log('No current user, skipping force refresh');
      return;
    }

    if (data.type === 'vouchers') {
      console.log('Force refresh DISABLED to prevent infinite loop:', data.department);
      // EMERGENCY FIX: Disable force refresh to stop infinite loop
      // store.fetchVouchers(data.department);
    }
  });

  // CRITICAL FIX: Handle force tab refresh for immediate dispatch updates
  socket.on('force_tab_refresh', (data) => {
    console.log('🔄 Force tab refresh received:', data);

    if (data.type === 'dispatch_confirmed') {
      // Dispatch custom event to trigger immediate tab refresh
      window.dispatchEvent(new CustomEvent('voucherUpdated', {
        detail: {
          type: 'dispatch_confirmed',
          voucherId: data.voucherId,
          department: data.department,
          forceTabRefresh: true,
          statusChanged: true,
          forceStatusRefresh: true,
          timestamp: data.timestamp
        }
      }));
      console.log(`🔄 REAL-TIME: Dispatched force tab refresh event for voucher ${data.voucherId}`);
    }
  });

  // Handle user joined
  socket.on('user_joined', (data) => {
    console.log('User joined:', data);

    // Show toast notification
    if (window.toast) {
      window.toast({
        title: "User Joined",
        description: `${data.userName} has joined the ${data.department} department`,
        variant: "default",
      });
    }

    // Force refresh users to update active users display
    const store = useAppStore.getState();
    store.fetchAllUsers();

    // CRITICAL FIX: Removed fetchVouchers call that was causing infinite loops
    // WebSocket events already provide real-time updates, no need for additional API calls
  });

  // Handle user left
  socket.on('user_left', (data) => {
    console.log('User left:', data);

    // Show toast notification
    if (window.toast) {
      window.toast({
        title: "User Left",
        description: `${data.userName} has left the ${data.department} department`,
        variant: "default",
      });
    }

    // Force refresh users to update active users display
    const store = useAppStore.getState();
    store.fetchAllUsers();

    // EMERGENCY FIX: Disable voucher refresh to stop infinite loop
    // const currentUser = store.currentUser;
    // if (currentUser && currentUser.department === data.department) {
    //   store.fetchVouchers(currentUser.department);
    // }
  });
};

// Helper functions for data updates
function handleVoucherUpdate(store: any, actionType: string, data: any) {
  const currentUser = store.currentUser;

  // Log current user info for debugging
  console.log('Current user in data_update:', currentUser ? `${currentUser.name} (${currentUser.department})` : 'Not logged in');

  // Skip processing if no current user
  if (!currentUser) {
    console.log('No current user, skipping voucher data_update');
    return;
  }

  // Check if this voucher belongs to the current user's department or if user is AUDIT/SYSTEM ADMIN
  const voucherDepartment = data.department;
  const isRelevantForUser =
    currentUser.department === 'AUDIT' ||
    currentUser.department === 'SYSTEM ADMIN' ||
    voucherDepartment === currentUser.department;

  if (!isRelevantForUser) {
    console.log(`Voucher from ${voucherDepartment} department not relevant for ${currentUser.department} user, skipping data_update`);
    return;
  }

  console.log(`Processing voucher data_update for ${voucherDepartment} department as ${currentUser.department} user`);

  switch (actionType) {
    case 'created':
      // CRITICAL FIX: Check if the voucher already exists before adding it
      const existingVoucher = store.vouchers.find(v => v.id === data.id || v.voucherId === data.voucher_id);
      if (!existingVoucher) {
        console.log('Adding new voucher from data_update (DIRECT STORE UPDATE):', data.voucher_id || data.id);

        // CRITICAL FIX: Format voucher for client compatibility
        const clientVoucher = {
          ...data,
          // Ensure all required client fields are present
          voucherId: data.voucher_id || data.voucherId,
          createdBy: data.created_by || data.createdBy || '',
          dispatchedBy: data.dispatched_by || data.dispatchedBy || '',
          sentToAudit: data.sent_to_audit !== undefined ? Boolean(data.sent_to_audit) : false,
          isReturned: data.is_returned !== undefined ? Boolean(data.is_returned) : false,
          pendingReturn: data.pending_return !== undefined ? Boolean(data.pending_return) : false,
          dispatched: data.dispatched !== undefined ? Boolean(data.dispatched) : false,
          lastUpdated: new Date().toISOString()
        };

        // Add voucher directly to store WITHOUT API call to prevent infinite loop
        const newVouchers = [...store.vouchers, clientVoucher];
        useAppStore.setState({ vouchers: newVouchers });

        console.log(`✅ Added voucher from data_update: ${clientVoucher.voucherId} (${clientVoucher.status})`);

        // Show toast notification for new voucher
        if (window.toast) {
          window.toast({
            title: "New Voucher Created",
            description: `Voucher ${clientVoucher.voucherId} has been created`,
            variant: "default",
          });
        }
      } else {
        console.log('Voucher already exists, not adding duplicate from data_update:', data.voucher_id || data.id);
      }
      break;

    case 'updated':
      if (store.updateVoucherFromWebSocket) {
        console.log('PRODUCTION FIX: Updating voucher from data_update for real-time tab movement:', data.voucher_id || data.id);

        // CRITICAL FIX: Convert server data format to client format for proper tab movement
        const clientVoucher = {
          ...data,
          // Ensure all required client fields are present
          voucherId: data.voucher_id || data.voucherId,
          dispatchedBy: data.dispatched_by || data.dispatchedBy || '',
          createdBy: data.created_by || data.createdBy || '',
          lastUpdated: new Date().toISOString(),

          // CRITICAL: Map server database fields to client fields for proper tab filtering
          sentToAudit: data.sent_to_audit !== undefined ? Boolean(data.sent_to_audit) : data.sentToAudit,
          isReturned: data.is_returned !== undefined ? Boolean(data.is_returned) : data.isReturned,
          pendingReturn: data.pending_return !== undefined ? Boolean(data.pending_return) : data.pendingReturn,
          dispatched: data.dispatched !== undefined ? Boolean(data.dispatched) : false,
          workStarted: data.work_started !== undefined ? Boolean(data.work_started) : data.workStarted,
          auditDispatchTime: data.audit_dispatch_time || data.auditDispatchTime,
          auditDispatchedBy: data.audit_dispatched_by || data.auditDispatchedBy
        };

        // Use WebSocket update method for consistent real-time updates
        store.updateVoucherFromWebSocket(data.id, clientVoucher);

        // Show minimal notification for status changes only
        if (data.status && window.toast) {
          window.toast({
            title: "Voucher Status Updated",
            description: `${data.voucher_id || data.id} is now ${data.status}`,
            variant: "default",
          });
        }
      }
      break;

    case 'deleted':
      if (store.deleteVoucher) {
        console.log('Deleting voucher from data_update:', data.voucher_id || data.id);
        store.deleteVoucher(data.id);

        // Show toast notification for deleted voucher
        if (window.toast) {
          window.toast({
            title: "Voucher Deleted",
            description: `Voucher ${data.voucher_id || data.id} has been deleted`,
            variant: "default",
          });
        }

        // CRITICAL FIX: Removed fetchVouchers call that was causing infinite loops
        // WebSocket deletion event already removes the voucher from store
      }
      break;
  }
}

function handleBatchUpdate(store: any, actionType: string, data: any) {
  switch (actionType) {
    case 'created':
      if (store.addBatch) {
        store.addBatch(data);
      }
      break;

    case 'updated':
      if (store.updateBatch) {
        store.updateBatch(data.id, data);
      }
      break;

    case 'deleted':
      if (store.deleteBatch) {
        store.deleteBatch(data.id);
      }
      break;
  }
}

function handleUserUpdate(store: any, actionType: string, data: any) {
  switch (actionType) {
    case 'created':
      if (store.addUser) {
        store.addUser(data);
      }
      break;

    case 'updated':
      if (store.updateUser) {
        store.updateUser(data.id, data);
      }
      break;

    case 'deleted':
      if (store.deleteUser) {
        store.deleteUser(data.id);
      }
      break;
  }
}

function handleNotificationUpdate(store: any, actionType: string, data: any) {
  switch (actionType) {
    case 'created':
      if (store.addNotification) {
        store.addNotification(data);
      }
      break;

    case 'updated':
      if (store.updateNotification) {
        store.updateNotification(data.id, data);
      }
      break;

    case 'deleted':
      if (store.deleteNotification) {
        store.deleteNotification(data.id);
      }
      break;
  }
}

function handleRegistrationUpdate(store: any, actionType: string, data: any) {
  switch (actionType) {
    case 'created':
      if (store.pendingRegistrations) {
        const pendingRegistrations = store.pendingRegistrations || [];
        const exists = pendingRegistrations.some(reg => reg.id === data.id);

        if (!exists && store.setPendingRegistrations) {
          store.setPendingRegistrations([...pendingRegistrations, data]);
        }
      }
      break;

    case 'approved':
    case 'rejected':
      if (store.pendingRegistrations && store.setPendingRegistrations) {
        const updatedRegistrations = (store.pendingRegistrations || []).filter(
          reg => reg.id !== data.id
        );
        store.setPendingRegistrations(updatedRegistrations);
      }
      break;
  }
}

// Track resource viewing and editing
// PRODUCTION-LEVEL: Updated viewResource function with optimized socket handling
export const viewResource = (resourceType: string, resourceId: string, isViewing: boolean, targetDepartment?: string): Promise<any> => {
  return new Promise((resolve) => {
    const socket = getSocket();
    if (!socket || !socket.connected) {
      console.warn('🔍 VIEW: Socket not available for view resource');
      resolve({ success: false, message: 'Socket not connected' });
      return;
    }

    const currentUser = useAppStore.getState().currentUser;
    const timestamp = Date.now();

    // Include targetDepartment if provided (for Audit department-specific resources)
    socket.emit('view_resource', {
      resourceType,
      resourceId,
      targetDepartment,
      isViewing,
      userId: currentUser?.id,
      userName: currentUser?.name,
      timestamp,
      action: isViewing ? 'EDITING' : 'OPENED'
    }, (response: any) => {
      resolve(response);
    });
  });
};

// Add inactivity timer for auto-release
export const startInactivityTimer = (resourceType: string, resourceId: string, targetDepartment?: string) => {
  // Clear any existing timer
  if (inactivityTimer) {
    clearTimeout(inactivityTimer);
  }

  // Set new timer
  inactivityTimer = setTimeout(() => {
    // Auto-release the lock after inactivity
    releaseLock(resourceType, resourceId, targetDepartment).then(() => {
      if (window.toast) {
        window.toast({
          title: "Lock Released",
          description: "Your editing access has been released due to inactivity",
          variant: "default",
        });
      }

      // Update the resource state to OPENED
      viewResource(resourceType, resourceId, false, targetDepartment);
    });
  }, INACTIVITY_TIMEOUT);
};

export const resetInactivityTimer = (resourceType: string, resourceId: string, targetDepartment?: string) => {
  // Reset the timer when there's activity
  if (inactivityTimer) {
    clearTimeout(inactivityTimer);
  }
  startInactivityTimer(resourceType, resourceId, targetDepartment);
};

// PRODUCTION-LEVEL: Updated requestLock function with optimized socket handling
export const requestLock = (resourceType: string, resourceId: string, targetDepartment?: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const socket = getSocket();
    if (!socket || !socket.connected) {
      console.warn('🔒 LOCK: Socket not available for lock request');
      resolve(false);
      return;
    }

    const currentUser = useAppStore.getState().currentUser;

    socket.emit('lock_request', {
      resourceType,
      resourceId,
      targetDepartment,
      userId: currentUser?.id,
      userName: currentUser?.name,
      action: 'EDITING'
    }, (response: any) => {
      if (response.success) {
        // Start inactivity timer when lock is acquired
        startInactivityTimer(resourceType, resourceId, targetDepartment);

        // Update the resource state to EDITING
        viewResource(resourceType, resourceId, true, targetDepartment);
      }
      resolve(response.success);
    });
  });
};

// Update the releaseLock function
// PRODUCTION-LEVEL: Updated releaseLock function with optimized socket handling
export const releaseLock = (resourceType: string, resourceId: string, targetDepartment?: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const socket = getSocket();
    if (!socket || !socket.connected) {
      console.warn('🔒 LOCK: Socket not available for lock release');
      resolve(false);
      return;
    }

    // Clear inactivity timer
    if (inactivityTimer) {
      clearTimeout(inactivityTimer);
      inactivityTimer = null;
    }

    const currentUser = useAppStore.getState().currentUser;

    socket.emit('lock_release', {
      resourceType,
      resourceId,
      targetDepartment,
      userId: currentUser?.id,
      userName: currentUser?.name,
      action: 'OPENED'
    }, (response: any) => {
      if (response.success) {
        // Update the resource state to OPENED
        viewResource(resourceType, resourceId, false, targetDepartment);
      }
      resolve(response.success);
    });
  });
};

// Update the sendActivity function to reset the inactivity timer
// PRODUCTION-LEVEL: Updated sendActivity function with optimized socket handling
export const sendActivity = (resourceType: string, resourceId: string, targetDepartment?: string): Promise<any> => {
  return new Promise((resolve) => {
    const socket = getSocket();
    if (!socket || !socket.connected) {
      console.warn('📡 ACTIVITY: Socket not available for send activity');
      resolve({ success: false, message: 'Socket not connected' });
      return;
    }

    // Reset inactivity timer
    resetInactivityTimer(resourceType, resourceId, targetDepartment);

    const currentUser = useAppStore.getState().currentUser;

    socket.emit('activity', {
      resourceType,
      resourceId,
      targetDepartment,
      userId: currentUser?.id,
      userName: currentUser?.name,
      timestamp: Date.now()
    }, (response: any) => {
      resolve(response);
    });
  });
};

// PRODUCTION-LEVEL: Updated sendStateUpdate function with optimized socket handling
export const sendStateUpdate = (updateData: any): void => {
  const socket = getSocket();
  if (!socket || !socket.connected) {
    console.warn('📡 STATE: Socket not available for state update');
    return;
  }

  socket.emit('state_update', updateData);
};

// Start heartbeat to keep connection alive
const startHeartbeat = (): void => {
  if (!socket) {
    return;
  }

  // Send heartbeat every 10 seconds (LAN optimized)
  const heartbeatInterval = setInterval(() => {
    if (!socket) {
      clearInterval(heartbeatInterval);
      return;
    }

    socket.emit('heartbeat');
  }, LAN_HEARTBEAT_INTERVAL);

  // Clear interval on disconnect
  socket.on('disconnect', () => {
    clearInterval(heartbeatInterval);
  });
};

// Stop heartbeat
const stopHeartbeat = (): void => {
  if (!socket) {
    return;
  }

  // Clear all intervals
  socket.removeAllListeners('disconnect');
};

// Export socket service
export default {
  connectSocket,
  disconnectSocket,
  getSocket,
  requestLock,
  releaseLock,
  sendStateUpdate,
  viewResource,
  sendActivity,
};
