import { Server, Socket } from 'socket.io';
import { logger } from '../utils/logger.js';
import { query } from '../database/db.js';
import { eventDeduplicator } from '../utils/EventDeduplicator.js';
import { circuitBreakerManager } from '../utils/CircuitBreaker.js';

// ENHANCED: Connected clients with health monitoring
const connectedClients: Map<string, {
  userId: string,
  userName: string,
  department: string,
  socket: Socket,
  lastActivity: number,
  lastHeartbeat: number, // Track heartbeat for connection health
  connectionTime: number, // Track when connection was established
  reconnectCount: number, // Track reconnection attempts
  viewingResources: Set<string> // Track which resources the user is viewing
}> = new Map();

// REDESIGNED: Clean Department Voucher Hub Locks
interface DepartmentLock {
  departmentName: string;           // FINANCE, AUDIT, MINISTRIES, etc.
  ownerId: string;                  // User ID who owns the lock
  ownerName: string;                // User name for display
  ownerDepartment: string;          // Owner's home department
  acquiredAt: number;               // Timestamp
  expiresAt: number;                // Auto-expiry
  lastActivity: number;             // For inactivity tracking
}

// Simple department locks storage
const departmentLocks: Map<string, DepartmentLock> = new Map();

// REDESIGNED: Clean lock system constants
const LOCK_EXPIRATION = 15 * 60 * 1000; // 15 minutes
const ACTIVITY_TIMEOUT = 10 * 60 * 1000; // 10 minutes
const DEPARTMENTS = ['FINANCE', 'AUDIT', 'MINISTRIES', 'PENSIONS', 'PENTMEDIA', 'MISSIONS', 'PENTSOS', 'INTERNAL AUDIT'] as const;

// Maximum concurrent users per department (kept for connection management)
const MAX_CONCURRENT_USERS: Record<string, number> = {
  'FINANCE': 10,
  'AUDIT': 10,
  'MINISTRIES': 10,
  'PENSIONS': 10,
  'PENTMEDIA': 10,
  'MISSIONS': 10,
  'PENTSOS': 10,
  'ADMINISTRATOR': 5,
  'SYSTEM ADMIN': 5
};

// REDESIGNED: Clean Department Lock Management Functions
function cleanupExpiredDepartmentLocks(): void {
  const now = Date.now();
  const expiredDepartments: string[] = [];

  for (const [department, lock] of departmentLocks.entries()) {
    if (lock.expiresAt < now) {
      expiredDepartments.push(department);
    }
  }

  for (const department of expiredDepartments) {
    departmentLocks.delete(department);
    logger.info(`🧹 Expired lock cleaned up for department: ${department}`);
    // Broadcast lock release to all clients
    if (ioInstance) {
      ioInstance.emit('department_lock_update', {
        department,
        isLocked: false,
        owner: null
      });
    }
  }
}

function acquireDepartmentLock(department: string, userId: string, userName: string, userDepartment: string): { success: boolean; message: string; lock?: DepartmentLock } {
  // Clean expired locks first
  cleanupExpiredDepartmentLocks();

  // Check if department is already locked
  const existingLock = departmentLocks.get(department);
  if (existingLock) {
    if (existingLock.ownerId === userId) {
      // User already owns this lock, refresh it
      existingLock.lastActivity = Date.now();
      existingLock.expiresAt = Date.now() + LOCK_EXPIRATION;
      logger.info(`🔄 Refreshed existing lock for ${department} by ${userName}`);
      return { success: true, message: 'Lock refreshed', lock: existingLock };
    } else {
      // Department is locked by someone else
      return {
        success: false,
        message: `${department} voucher hub is currently being accessed by ${existingLock.ownerName}. Please wait until they finish.`
      };
    }
  }

  // Create new lock
  const now = Date.now();
  const newLock: DepartmentLock = {
    departmentName: department,
    ownerId: userId,
    ownerName: userName,
    ownerDepartment: userDepartment,
    acquiredAt: now,
    expiresAt: now + LOCK_EXPIRATION,
    lastActivity: now
  };

  departmentLocks.set(department, newLock);

  // Enhanced logging to show the lock type
  const lockType = department.startsWith('AUDIT-VOUCHER-HUB-') ? 'VOUCHER HUB' :
                   department.startsWith('DEPARTMENT-DASHBOARD-') ? 'DEPARTMENT DASHBOARD' : 'LEGACY';
  logger.info(`🔒 ISOLATED ACCESS: ${lockType} lock acquired - ${department} by ${userName} (${userDepartment})`);

  // Broadcast lock acquisition to all clients
  if (ioInstance) {
    ioInstance.emit('department_lock_update', {
      department,
      isLocked: true,
      owner: {
        id: userId,
        name: userName,
        department: userDepartment
      }
    });

    // EXCLUSIVE ACCESS: Broadcast to kick out any other audit users from this department hub
    ioInstance.emit('department_access_blocked', {
      department,
      blockedBy: {
        id: userId,
        name: userName,
        department: userDepartment
      },
      message: `${userName} is currently accessing ${department} voucher hub. Access is temporarily restricted.`
    });
  }

  return { success: true, message: 'Exclusive access acquired successfully', lock: newLock };
}

function releaseDepartmentLock(department: string, userId: string): { success: boolean; message: string } {
  const lock = departmentLocks.get(department);

  if (!lock) {
    return { success: false, message: 'No lock exists for this department' };
  }

  if (lock.ownerId !== userId) {
    return { success: false, message: 'You do not own this lock' };
  }

  departmentLocks.delete(department);

  // Enhanced logging to show the lock type being released
  const lockType = department.startsWith('AUDIT-VOUCHER-HUB-') ? 'VOUCHER HUB' :
                   department.startsWith('DEPARTMENT-DASHBOARD-') ? 'DEPARTMENT DASHBOARD' : 'LEGACY';
  logger.info(`🔓 ISOLATED ACCESS RELEASED: ${lockType} lock released - ${department} by ${lock.ownerName}`);

  // Broadcast lock release to all clients
  if (ioInstance) {
    ioInstance.emit('department_lock_update', {
      department,
      isLocked: false,
      owner: null
    });

    // EXCLUSIVE ACCESS: Broadcast that department is now available
    ioInstance.emit('department_access_restored', {
      department,
      message: `${department} voucher hub is now available for access.`
    });
  }

  return { success: true, message: 'Exclusive access released successfully' };
}

function getDepartmentLockStatus(department: string): { isLocked: boolean; owner?: { id: string; name: string; department: string } } {
  cleanupExpiredDepartmentLocks();

  const lock = departmentLocks.get(department);
  if (!lock) {
    return { isLocked: false };
  }

  return {
    isLocked: true,
    owner: {
      id: lock.ownerId,
      name: lock.ownerName,
      department: lock.ownerDepartment
    }
  };
}

// Setup socket handlers
export function setupSocketHandlers(io: Server) {
  logger.info('🔧 Setting up Socket.IO middleware and handlers...');

  // LAN MODE: Extract user from session cookies for WebSocket authentication
  io.use(async (socket, next) => {
    logger.info('🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!');
    try {
      logger.info(`WebSocket connection from ${socket.handshake.address} - LAN mode, extracting user from session`);

      // Extract session from cookies
      let authenticatedUser = null;

      try {
        const cookies = socket.handshake.headers.cookie;
        if (cookies) {
          // CRITICAL FIX: Parse session ID from cookies using correct cookie name
          // Must match the cookie name set in auth.ts (vms_session_id)
          const sessionMatch = cookies.match(/vms_session_id=([^;]+)/) || cookies.match(/session_id=([^;]+)/);
          if (sessionMatch) {
            const sessionId = sessionMatch[1];

            // PRODUCTION FIX: Add timeout to database query to prevent hanging
            const queryPromise = query(
              'SELECT user_id, user_name, department FROM active_sessions WHERE id = ? AND is_active = TRUE',
              [sessionId]
            );

            // Add 3-second timeout to prevent WebSocket connection hanging
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Database query timeout')), 3000);
            });

            const sessions = await Promise.race([queryPromise, timeoutPromise]) as any[];

            if (sessions && sessions.length > 0) {
              const session = sessions[0];
              authenticatedUser = {
                id: session.user_id,
                name: session.user_name,
                department: session.department,
                role: session.role || 'user',
                isAuthenticated: true
              };

              logger.info(`🔐 WebSocket authenticated user from session: ${session.user_name} (${session.department})`);
            }
          }
        }
      } catch (sessionError) {
        logger.warn('Failed to extract user from session (using fallback):', sessionError instanceof Error ? sessionError.message : String(sessionError));
      }

      // Set user data (fallback to LAN user if no session found)
      socket.data.user = authenticatedUser || {
        id: 'lan-user',
        name: 'LAN User',
        department: 'GUEST',
        role: 'user',
        isAuthenticated: true // LAN users are considered authenticated
      };

      next();
    } catch (error) {
      logger.error('❌ Socket setup error:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        socketId: socket.id,
        timestamp: new Date().toISOString()
      });

      // ROBUSTNESS: Still allow connection with fallback user for system resilience
      socket.data.user = {
        id: 'lan-user',
        name: 'LAN User',
        department: 'GUEST',
        role: 'user',
        isAuthenticated: true
      };

      // MONITORING: Track error for system health
      logger.warn('⚠️ Using fallback authentication due to setup error');
      next();
    }
  });

  // Connection handler
  io.on('connection', (socket) => {
    const userId = socket.data.user.id;
    const userName = socket.data.user.name;
    const department = socket.data.user.department;
    const isAuthenticated = socket.data.user.isAuthenticated;

    logger.info(`🔌 WebSocket connection established: ${userName} (${userId}) from ${department} [Auth: ${isAuthenticated ? 'YES' : 'NO'}]`);

    // If not authenticated, handle differently
    if (!isAuthenticated) {
      logger.warn(`⚠️ Unauthenticated WebSocket connection from ${socket.handshake.address}`);

      // Send authentication required message
      socket.emit('authentication_required', {
        message: 'Please provide a valid authentication token',
        timestamp: Date.now()
      });

      // LAN MODE: No post-connection authentication needed
      socket.on('authenticate', async (data) => {
        try {
          logger.info('Authentication request received - LAN mode, automatically successful');

          // In LAN mode, all connections are automatically authenticated
          socket.emit('authentication_success', {
            user: socket.data.user,
            message: 'LAN mode - authentication not required'
          });

          // Continue with normal connection flow using default LAN user
          setupAuthenticatedConnection(socket, socket.data.user.id, socket.data.user.name, socket.data.user.department);

        } catch (error) {
          logger.error('Post-connection authentication error:', error);
          socket.emit('authentication_failed', { error: 'Authentication failed' });
        }
      });

      // Don't continue with normal flow for unauthenticated users
      return;
    }

    // Continue with authenticated connection flow
    setupAuthenticatedConnection(socket, userId, userName, department);
  });
}

// Separate function for authenticated connection setup
async function setupAuthenticatedConnection(socket: any, userId: string, userName: string, department: string) {
  logger.info(`🚀 Setting up authenticated connection for ${userName} (${userId}) from ${department}`);

  // Check if this user is already connected from another device/browser
  const existingConnections = Array.from(connectedClients.values())
    .filter(client => client.userId === userId && client.socket.id !== socket.id);

  if (existingConnections.length > 0) {
    logger.info(`User ${userName} (${userId}) already connected from ${existingConnections.length} other sessions`);

    // We'll allow multiple connections, but log them for monitoring
    existingConnections.forEach(conn => {
      logger.info(`Existing connection: ${conn.socket.id} with last activity at ${new Date(conn.lastActivity).toISOString()}`);
    });
  }

  // ENHANCED: Add to connected clients with health monitoring
  const now = Date.now();
  connectedClients.set(socket.id, {
    userId,
    userName,
    department,
    socket,
    lastActivity: now,
    lastHeartbeat: now, // Initialize heartbeat timestamp
    connectionTime: now,
    reconnectCount: 0, // Track reconnection attempts
    viewingResources: new Set() // Initialize empty set of viewed resources
  });

  // Join department-specific room
  socket.join(`department:${department}`);
  logger.info(`User ${userName} (${userId}) joined room: department:${department}`);

  // If user is AUDIT or SYSTEM ADMIN, also join those rooms
  if (department === 'AUDIT' || department === 'SYSTEM ADMIN') {
    socket.join('admin-users');
    logger.info(`User ${userName} (${userId}) joined room: admin-users`);
  }

  // Join user-specific room for targeted messages
  socket.join(`user:${userId}`);
  logger.info(`User ${userName} (${userId}) joined personal room: user:${userId}`);

  // Send current locks to the client
  sendLocksToClient(socket);

  // Broadcast updated connected users list for this department
  broadcastConnectedUsers(department);

  // FIXED: Update existing HTTP session with socket info instead of creating duplicate
  try {
    // Find the user's active HTTP session and update it with socket info
    await query(
      `UPDATE active_sessions
       SET socket_id = ?, last_activity = NOW()
       WHERE user_id = ? AND is_active = TRUE
       ORDER BY session_start DESC
       LIMIT 1`,
      [socket.id, userId]
    );
    logger.info(`Updated existing session with socket info for ${userName} (${userId})`);
  } catch (error) {
    logger.error('Error updating session with socket info:', error);
  }

  // Log the number of connected clients
  const totalClients = connectedClients.size;
  const departmentClients = Array.from(connectedClients.values())
    .filter(client => client.department === department).length;

  logger.info(`Total connected clients: ${totalClients}, ${departmentClients} in department ${department}`);
  console.log(`Connected clients: ${Array.from(connectedClients.values()).map(c => `${c.userName} (${c.userId})`).join(', ')}`);

  // Update activity timestamp on any event
  socket.onAny(() => {
    const client = connectedClients.get(socket.id);
    if (client) {
      client.lastActivity = Date.now();
      connectedClients.set(socket.id, client);
    }
  });

  // Force refresh vouchers for this user's department
  socket.emit('force_refresh', {
    type: 'vouchers',
    department: department,
    timestamp: Date.now()
  });

  // Log all rooms this socket is in
  const rooms = Array.from(socket.rooms.values());
  logger.info(`Socket ${socket.id} is in rooms: ${rooms.join(', ')}`);

  // Notify other users in the same department that a new user has connected
  socket.to(`department:${department}`).emit('user_joined', {
    userId,
    userName,
    department,
    timestamp: Date.now()
  });

    // Handle get_department_users request
    socket.on('get_department_users', (data: any, callback: any) => {
      try {
        const { department: requestedDept } = data;
        const now = Date.now();

        // FIXED: Get unique users only (no duplicates)
        const uniqueUsers = new Map<string, any>();

        Array.from(connectedClients.values())
          .filter(client => client.department === requestedDept)
          .forEach(client => {
            const isActive = now - client.lastActivity < ACTIVITY_TIMEOUT;

            // Only keep the most recent connection for each user
            const existingUser = uniqueUsers.get(client.userId);
            if (!existingUser || client.lastActivity > existingUser.lastActivity) {
              uniqueUsers.set(client.userId, {
                id: client.userId,
                name: client.userName,
                department: client.department,
                isActive: isActive,
                lastActivity: client.lastActivity,
                // Check if user has any department locks
                isEditing: Array.from(departmentLocks.values()).some(lock => lock.ownerId === client.userId)
              });
            }
          });

        const departmentUsers = Array.from(uniqueUsers.values());

        logger.info(`Returning ${departmentUsers.length} users for department ${requestedDept}`);

        callback({
          success: true,
          users: departmentUsers,
          department: requestedDept,
          timestamp: now
        });
      } catch (error) {
        logger.error('Error processing get_department_users request:', error);
        callback({ success: false, message: 'Server error' });
      }
    });

    // Handle get_locks request
    socket.on('get_locks', (callback: any) => {
      try {
        // Clean up expired department locks first
        cleanupExpiredDepartmentLocks();

        // Get all department locks
        const locks = Array.from(departmentLocks.entries()).map(([department, lock]) => ({
          key: `department:${department}`,
          userId: lock.ownerId,
          userName: lock.ownerName,
          department: lock.ownerDepartment,
          expiresAt: lock.expiresAt,
          resourceType: 'department',
          resourceId: department,
          // Calculate remaining time in seconds
          remainingTime: Math.max(0, Math.floor((lock.expiresAt - Date.now()) / 1000))
        }));

        logger.info(`Returning ${locks.length} active department locks`);
        if (locks.length > 0) {
          console.log('Active department locks:', locks.map(l => `${l.key} by ${l.userName} (expires in ${l.remainingTime}s)`));
        }

        callback({
          success: true,
          locks: locks,
          timestamp: Date.now()
        });
      } catch (error) {
        logger.error('Error processing get_locks request:', error);
        callback({ success: false, message: 'Server error' });
      }
    });

    // Handle disconnect
    socket.on('disconnect', async () => {
      logger.info(`User disconnected: ${userName} (${userId}) from department ${department}`);

      // Get the client before removing it
      const client = connectedClients.get(socket.id);

      // Broadcast resource viewer updates for all resources this user was viewing
      if (client && client.viewingResources.size > 0) {
        // Get all resources this user was viewing
        const viewedResources = Array.from(client.viewingResources);

        logger.info(`User ${userName} (${userId}) was viewing ${viewedResources.length} resources`);

        // Remove from connected clients
        connectedClients.delete(socket.id);

        // Broadcast updated viewer counts for each resource
        viewedResources.forEach(resourceKey => {
          broadcastResourceViewers(resourceKey);
        });
      } else {
        // Remove from connected clients
        connectedClients.delete(socket.id);
      }

      // Check if this was the user's last connection
      const remainingConnections = Array.from(connectedClients.values())
        .filter(client => client.userId === userId);

      if (remainingConnections.length === 0) {
        logger.info(`User ${userName} (${userId}) has no remaining connections`);

        // Release any department locks held by this user
        releaseUserDepartmentLocks(userId);
      } else {
        logger.info(`User ${userName} (${userId}) still has ${remainingConnections.length} active connections`);
      }

      // FIXED: Clear socket info from session but keep session active (user might still be logged in via HTTP)
      try {
        await query(
          `UPDATE active_sessions
           SET socket_id = NULL, last_activity = NOW()
           WHERE user_id = ? AND socket_id = ?`,
          [userId, socket.id]
        );
        logger.info(`Cleared socket info for ${userName} (${userId}) - session remains active`);
      } catch (error) {
        logger.error('Error clearing socket info from session:', error);
      }

      // Broadcast updated connected users list for this department
      broadcastConnectedUsers(department);

      // PRODUCTION FIX: Only notify if user was actually active (not just connection cleanup)
      // Check if this was a real user disconnect vs automatic cleanup
      const wasActiveUser = client && (Date.now() - client.lastActivity) < 30000; // Active within 30 seconds

      if (wasActiveUser) {
        socket.to(`department:${department}`).emit('user_left', {
          userId,
          userName,
          department,
          timestamp: Date.now(),
          reason: 'user_disconnect'
        });
        logger.info(`📢 Broadcasting user_left for active user: ${userName} (${department})`);
      } else {
        logger.info(`🔇 Skipping user_left broadcast for inactive connection: ${userName} (${department})`);
      }

      // Log the number of connected clients
      const totalClients = connectedClients.size;
      const departmentClients = Array.from(connectedClients.values())
        .filter(client => client.department === department).length;

      logger.info(`After disconnect - Total connected clients: ${totalClients}, ${departmentClients} in department ${department}`);
    });

    // ENHANCED: Handle heartbeat with connection health monitoring
    socket.on('heartbeat', () => {
      try {
        const client = connectedClients.get(socket.id);
        if (client) {
          client.lastHeartbeat = Date.now();

          // Respond with comprehensive health status
          socket.emit('heartbeat_ack', {
            timestamp: Date.now(),
            connectionId: socket.id,
            userId: client.userId,
            department: client.department,
            serverStatus: 'healthy'
          });
        } else {
          // Client not found - potential connection issue
          logger.warn(`⚠️ Heartbeat from unknown client: ${socket.id}`);
          socket.emit('heartbeat_ack', {
            timestamp: Date.now(),
            connectionId: socket.id,
            serverStatus: 'reconnect_required'
          });
        }
      } catch (error) {
        logger.error('❌ Error processing heartbeat:', error);
        socket.emit('heartbeat_ack', {
          timestamp: Date.now(),
          serverStatus: 'error'
        });
      }
    });

    // Handle resource view tracking
    socket.on('view_resource', (data: any, callback: any) => {
      const { resourceType, resourceId, targetDepartment, isViewing } = data;

      try {
        const client = connectedClients.get(socket.id);
        if (!client) {
          if (callback) callback({ success: false, message: 'Client not found' });
          return;
        }

        // For Audit users, we can specify which department's resources they want to view
        const effectiveResourceId = department === 'AUDIT' && targetDepartment ?
          `${targetDepartment}-${resourceId}` : resourceId;

        const resourceKey = `${resourceType}:${effectiveResourceId}`;

        // Update the client's viewing resources
        if (isViewing) {
          client.viewingResources.add(resourceKey);
          logger.info(`User ${userName} (${userId}) is now viewing ${resourceKey}`);
        } else {
          client.viewingResources.delete(resourceKey);
          logger.info(`User ${userName} (${userId}) is no longer viewing ${resourceKey}`);
        }

        // Update the client record
        client.lastActivity = Date.now();
        connectedClients.set(socket.id, client);

        // Broadcast the updated viewer count for this resource
        broadcastResourceViewers(resourceKey);

        // If this is an Audit user viewing a specific department's resources,
        // also broadcast to that department
        if (department === 'AUDIT' && targetDepartment) {
          broadcastConnectedUsers(targetDepartment);
        }

        // RACE CONDITION FIX: Disable automatic lock acquisition to prevent conflicts
        // Only allow manual lock acquisition through lock_request to ensure proper coordination
        // This prevents the race condition where multiple users try to acquire locks simultaneously

        // Log the viewing activity but don't auto-acquire locks
        logger.info(`User ${userName} (${userId}) is viewing ${resourceKey}${targetDepartment ? ' (targeting '+targetDepartment+')' : ''} - no auto-lock`);

        // Note: Users must explicitly request locks through the UI to ensure proper coordination

        if (callback) {
          callback({
            success: true,
            message: isViewing ? 'Now viewing resource' : 'No longer viewing resource',
            viewerCount: getResourceViewerCount(resourceKey)
          });
        }
      } catch (error) {
        logger.error('Error processing view_resource request:', error);
        if (callback) callback({ success: false, message: 'Server error' });
      }
    });

    // Handle activity update - used to extend lock expiration
    socket.on('activity', (data: any, callback: any) => {
      const { resourceType, resourceId, targetDepartment } = data;

      // For Audit users, we need to use the same key format as when acquiring the lock
      const effectiveResourceId = department === 'AUDIT' && targetDepartment ?
        `${targetDepartment}-${resourceId}` : resourceId;

      const lockKey = `${resourceType}:${effectiveResourceId}`;

      try {
        // For department locks, update activity if user owns the lock
        const departmentLock = departmentLocks.get(effectiveResourceId);
        if (departmentLock && departmentLock.ownerId === userId) {
          const now = Date.now();

          // PRODUCTION FIX: Throttle activity updates to prevent infinite loops
          // Only update if at least 5 seconds have passed since last activity update
          const timeSinceLastActivity = now - departmentLock.lastActivity;
          const ACTIVITY_THROTTLE_MS = 5000; // 5 seconds

          if (timeSinceLastActivity >= ACTIVITY_THROTTLE_MS) {
            // Update last activity time and extend expiration
            departmentLock.lastActivity = now;
            departmentLock.expiresAt = now + LOCK_EXPIRATION;
            departmentLocks.set(effectiveResourceId, departmentLock);

            logger.info(`Department lock activity update for ${effectiveResourceId} by ${userName} (${userId}) - throttled`);
          }

          if (callback) {
            callback({
              success: true,
              message: 'Activity recorded',
              expiresAt: departmentLock.expiresAt
            });
          }
        } else if (callback) {
          callback({
            success: false,
            message: 'Lock not found or not owned by you'
          });
        }
      } catch (error) {
        logger.error('Error processing activity update:', error);
        if (callback) {
          callback({ success: false, message: 'Server error' });
        }
      }
    });

    // REDESIGNED: Clean Department Lock Request Handler
    socket.on('department_lock_request', async (data: any, callback: any) => {
      const { targetDepartment } = data;

      // Validate department
      if (!targetDepartment || !DEPARTMENTS.includes(targetDepartment as any)) {
        callback({
          success: false,
          message: 'Invalid department specified'
        });
        return;
      }

      // CRITICAL FIX: Re-authenticate user from current session on every lock request
      // This prevents stale department information from being used
      let currentUserDepartment = department;
      let currentUserName = userName;
      let currentUserId = userId;

      try {
        const cookies = socket.handshake.headers.cookie;
        if (cookies) {
          const sessionMatch = cookies.match(/vms_session_id=([^;]+)/) || cookies.match(/session_id=([^;]+)/);
          if (sessionMatch) {
            const sessionId = sessionMatch[1];
            const sessions = await query(
              'SELECT user_id, user_name, department FROM active_sessions WHERE id = ? AND is_active = TRUE',
              [sessionId]
            ) as any[];

            if (sessions && sessions.length > 0) {
              const session = sessions[0];
              currentUserId = session.user_id;
              currentUserName = session.user_name;
              currentUserDepartment = session.department;

              logger.info(`🔄 LOCK REQUEST: Re-authenticated ${currentUserName} (${currentUserDepartment}) from current session`);
            } else {
              logger.warn(`🚫 LOCK REQUEST: No active session found for session ID: ${sessionId}`);
              callback({
                success: false,
                message: 'Session expired. Please refresh the page and try again.'
              });
              return;
            }
          }
        }
      } catch (sessionError) {
        logger.error('Failed to re-authenticate user for lock request:', sessionError);
        callback({
          success: false,
          message: 'Authentication error. Please refresh the page and try again.'
        });
        return;
      }

      // ORIGINAL ARCHITECTURE: Department Voucher Hubs belong to AUDIT users exclusively
      // AUDIT users have EXCLUSIVE access to all Department Voucher Hubs (FINANCE, MINISTRIES, etc.)
      // Other departments can only lock their own department
      if (currentUserDepartment !== 'AUDIT' && targetDepartment !== currentUserDepartment) {
        logger.warn(`🚫 SECURITY VIOLATION: ${currentUserName} (${currentUserDepartment}) attempted to lock ${targetDepartment} - DENIED`);
        callback({
          success: false,
          message: `Access denied: You can only acquire locks on your own department (${currentUserDepartment})`
        });
        return;
      }

      // AUDIT users have exclusive access to all Department Voucher Hubs
      if (currentUserDepartment === 'AUDIT') {
        logger.info(`✅ AUDIT EXCLUSIVE ACCESS: ${currentUserName} accessing ${targetDepartment} voucher hub`);
      }

      // CRITICAL ARCHITECTURE FIX: Create completely separate lock namespaces
      // AUDIT users working in voucher hubs should NEVER conflict with department users receiving batches
      // This ensures AUDIT voucher hub access is independent of department dashboard operations
      let lockKey: string;

      if (currentUserDepartment === 'AUDIT') {
        // AUDIT users get voucher hub locks (for processing vouchers in department hubs)
        lockKey = `AUDIT-VOUCHER-HUB-${targetDepartment}`;
      } else {
        // Department users get dashboard locks (for batch receiving in their own dashboard)
        lockKey = `DEPARTMENT-DASHBOARD-${targetDepartment}`;
      }

      logger.info(`🔒 ISOLATED LOCK REQUEST: ${currentUserName} (${currentUserDepartment}) requesting ${currentUserDepartment === 'AUDIT' ? 'voucher hub' : 'dashboard'} lock: ${lockKey}`);

      const result = acquireDepartmentLock(lockKey, currentUserId, currentUserName, currentUserDepartment);
      callback(result);
    });


    // REDESIGNED: Clean Department Lock Release Handler
    socket.on('department_lock_release', (data: any, callback: any) => {
      const { targetDepartment } = data;

      if (!targetDepartment) {
        callback({
          success: false,
          message: 'Department not specified'
        });
        return;
      }

      // CRITICAL ARCHITECTURE FIX: Use same lock key structure as acquisition
      const lockKey = department === 'AUDIT' ? `AUDIT-VOUCHER-HUB-${targetDepartment}` : `DEPARTMENT-DASHBOARD-${targetDepartment}`;

      logger.info(`🔓 DEPARTMENT LOCK RELEASE: ${userName} (${department}) releasing lock for ${lockKey}`);

      const result = releaseDepartmentLock(lockKey, userId);
      callback(result);
    });

    // REDESIGNED: Get Department Lock Status Handler
    socket.on('get_department_lock_status', (data: any, callback: any) => {
      const { targetDepartment } = data;

      if (!targetDepartment) {
        callback({
          success: false,
          message: 'Department not specified'
        });
        return;
      }

      const status = getDepartmentLockStatus(targetDepartment);
      callback({
        success: true,
        ...status
      });
    });

    // REDESIGNED: Get All Department Locks Status
    socket.on('get_all_department_locks', (callback: any) => {
      cleanupExpiredDepartmentLocks();

      const allLocks: Record<string, any> = {};
      for (const dept of DEPARTMENTS) {
        allLocks[dept] = getDepartmentLockStatus(dept);
      }

      callback({
        success: true,
        locks: allLocks
      });
    });

    // AUTO-RELEASE: Handle navigation-based lock release
    socket.on('navigation_lock_release', (data: any, callback: any) => {
      const { targetDepartment } = data;

      if (!targetDepartment) {
        callback({
          success: false,
          message: 'Department not specified'
        });
        return;
      }

      // CRITICAL ARCHITECTURE FIX: Use same lock key structure as acquisition
      const lockKey = department === 'AUDIT' ? `AUDIT-VOUCHER-HUB-${targetDepartment}` : `DEPARTMENT-DASHBOARD-${targetDepartment}`;

      logger.info(`🔓 NAVIGATION RELEASE: ${userName} (${department}) releasing lock for ${lockKey} due to navigation`);

      const result = releaseDepartmentLock(lockKey, userId);
      callback(result);
    });

    // EXCLUSIVE ACCESS: Check if user can access department hub
    socket.on('check_department_access', (data: any, callback: any) => {
      const { targetDepartment } = data;

      if (!targetDepartment) {
        callback({
          success: false,
          message: 'Department not specified'
        });
        return;
      }

      // For non-audit users, check if their own department is locked by another user
      if (department !== 'AUDIT') {
        if (targetDepartment === department) {
          // CRITICAL FIX: Check if department is already locked by another user from same department
          const lockStatus = getDepartmentLockStatus(targetDepartment);

          if (lockStatus.isLocked && lockStatus.owner?.id !== userId) {
            // Department is locked by another user from the same department
            callback({
              success: true,
              canAccess: false,
              isBlocked: true,
              blockedBy: lockStatus.owner,
              message: `${targetDepartment} department is currently being accessed by ${lockStatus.owner?.name}. Please wait until they finish.`
            });
          } else {
            // Department is available or locked by current user
            callback({
              success: true,
              canAccess: true,
              message: lockStatus.isLocked ? 'You currently have access to this department' : 'Department is available for access'
            });
          }
        } else {
          callback({
            success: true,
            canAccess: false,
            message: 'You can only access your own department'
          });
        }
        return;
      }

      // For audit users, check if department is locked by another audit user
      // Use AUDIT-specific lock key to prevent conflicts with actual department users
      const lockKey = `AUDIT-${targetDepartment}`;
      const lockStatus = getDepartmentLockStatus(lockKey);

      if (lockStatus.isLocked && lockStatus.owner?.id !== userId) {
        // Department is locked by another audit user
        callback({
          success: true,
          canAccess: false,
          isBlocked: true,
          blockedBy: lockStatus.owner,
          message: `${targetDepartment} voucher hub is currently being accessed by ${lockStatus.owner?.name}. Please wait until they finish.`
        });
      } else {
        // Department is available or locked by current user
        callback({
          success: true,
          canAccess: true,
          message: lockStatus.isLocked ? 'You currently have access to this department' : 'Department is available for access'
        });
      }
    });

    // Handle state update
    socket.on('state_update', (data: any) => {
      // Broadcast state update to all clients except sender
      socket.broadcast.emit('state_update', data);
    });

    // CRITICAL FIX: Handle explicit department join
    socket.on('join_department', (data: any) => {
      const { department, userId, userName } = data;

      logger.info(`User ${userName} (${userId}) explicitly joining department:${department} room`);
      console.log(`User ${userName} (${userId}) explicitly joining department:${department} room`);

      // Join department-specific room
      socket.join(`department:${department}`);

      // REAL-TIME FIX: Send confirmation back to client
      socket.emit('joined_department', {
        department,
        userId,
        userName,
        success: true,
        message: `Successfully joined ${department} department room`
      });

      logger.info(`✅ REAL-TIME: Confirmed department join for ${userName} in ${department}`);

      // Update client record
      const client = connectedClients.get(socket.id);
      if (client) {
        client.lastActivity = Date.now();
        connectedClients.set(socket.id, client);
      } else {
        // ENHANCED: If client record doesn't exist, create it with health monitoring
        logger.info(`Creating new client record for ${userName} (${userId}) in department ${department}`);
        const now = Date.now();
        connectedClients.set(socket.id, {
          userId,
          userName,
          department,
          socket,
          lastActivity: now,
          lastHeartbeat: now, // Initialize heartbeat timestamp
          connectionTime: now,
          reconnectCount: 0, // Track reconnection attempts
          viewingResources: new Set() // Initialize empty set of viewed resources
        });
      }

      // Log all connected clients for debugging
      logger.info(`Connected clients after join: ${Array.from(connectedClients.values()).map(c => `${c.userName} (${c.department})`).join(', ')}`);
      console.log(`Connected clients after join: ${Array.from(connectedClients.values()).map(c => `${c.userName} (${c.department})`).join(', ')}`);

      // Broadcast updated connected users list for this department
      broadcastConnectedUsers(department);
    });

    // REMOVED: Duplicate presence announcement handler that was causing triple notifications

  // REDESIGNED: Periodically clean up expired department locks
  setInterval(() => {
    try {
      cleanupExpiredDepartmentLocks();
    } catch (error) {
      logger.error('❌ Error during department lock cleanup:', error);
    }
  }, 30000); // Check every 30 seconds

  // ROBUSTNESS: Monitor connection health and cleanup stale connections
  setInterval(() => {
    try {
      cleanupStaleConnections();
    } catch (error) {
      logger.error('❌ Error during connection cleanup:', error);
    }
  }, 60000); // Check every 60 seconds

  // ROBUSTNESS: System health monitoring and recovery
  setInterval(() => {
    try {
      performSystemHealthCheck();
    } catch (error) {
      logger.error('❌ Error during system health check:', error);
    }
  }, 300000); // Check every 5 minutes
}

// Send current department locks to a client
function sendLocksToClient(socket: Socket) {
  // Clean up expired department locks first
  cleanupExpiredDepartmentLocks();

  const now = Date.now();

  const locks = Array.from(departmentLocks.entries()).map(([department, lock]) => {
    return {
      key: `department:${department}`,
      userId: lock.ownerId,
      userName: lock.ownerName || 'Unknown',
      department: lock.ownerDepartment,
      expiresAt: lock.expiresAt,
      resourceType: 'department',
      resourceId: department,
      // Calculate remaining time in seconds
      remainingTime: Math.max(0, Math.floor((lock.expiresAt - now) / 1000))
    };
  });

  socket.emit('locks_update', {
    locks,
    timestamp: now
  });

  logger.info(`Sent ${locks.length} active locks to client`);
}

// Store the io instance
let ioInstance: Server | null = null;

// Set the io instance
export function setIoInstance(io: Server) {
  ioInstance = io;
}





// REDESIGNED: Release all department locks held by a user
function releaseUserDepartmentLocks(userId: string): number {
  let releasedCount = 0;
  const releasedDepartments: string[] = [];

  for (const [department, lock] of departmentLocks.entries()) {
    if (lock.ownerId === userId) {
      departmentLocks.delete(department);
      releasedDepartments.push(department);
      releasedCount++;

      logger.info(`🔓 Auto-released department lock for ${department} (user ${lock.ownerName} disconnected)`);

      // Broadcast lock release
      if (ioInstance) {
        ioInstance.emit('department_lock_update', {
          department,
          isLocked: false,
          owner: null
        });
      }
    }
  }

  if (releasedCount > 0) {
    logger.info(`Released ${releasedCount} department locks for disconnected user ${userId}: ${releasedDepartments.join(', ')}`);
  }

  return releasedCount;
}

// REDESIGNED: Clear all department locks (for admin/testing purposes)
function clearAllDepartmentLocks(): number {
  const lockCount = departmentLocks.size;
  const clearedDepartments = Array.from(departmentLocks.keys());

  if (lockCount > 0) {
    departmentLocks.clear();

    // Broadcast updates for all cleared locks
    if (ioInstance) {
      for (const department of clearedDepartments) {
        ioInstance.emit('department_lock_update', {
          department,
          isLocked: false,
          owner: null
        });
      }
    }

    logger.info(`🧹 Admin cleared all ${lockCount} department locks: ${clearedDepartments.join(', ')}`);
  }

  return lockCount;
}

// Broadcast user update to all clients
export function broadcastUserUpdate(type: string, userData: any) {
  if (!ioInstance) return;

  logger.info(`Broadcasting user update: ${type} for user ${userData.name} (${userData.id})`);
  console.log(`Broadcasting user update: ${type}`, userData);

  // Get all connected clients
  const clientCount = ioInstance.sockets.sockets.size;
  logger.info(`Broadcasting to ${clientCount} connected clients`);

  // Make sure isActive is a boolean for consistency
  const normalizedUserData = {
    ...userData,
    isActive: Boolean(userData.isActive || userData.is_active)
  };

  // Remove any internal properties
  delete normalizedUserData._originalIsActive;

  // Log the normalized data
  console.log(`Broadcasting normalized user data:`, normalizedUserData);

  // Emit to all clients
  ioInstance.emit('user_update', {
    type,
    user: normalizedUserData,
    timestamp: Date.now()
  });

  // Also emit to specific department room if available
  if (userData.department) {
    logger.info(`Emitting to department room: ${userData.department}`);
    ioInstance.to(`department:${userData.department}`).emit('user_update', {
      type,
      user: normalizedUserData,
      timestamp: Date.now()
    });
  }

  // Also emit to AUDIT and SYSTEM ADMIN rooms
  ioInstance.to('department:AUDIT').emit('user_update', {
    type,
    user: normalizedUserData,
    timestamp: Date.now()
  });

  ioInstance.to('department:SYSTEM ADMIN').emit('user_update', {
    type,
    user: normalizedUserData,
    timestamp: Date.now()
  });

  // If this is a user status change, update the connected users list
  if (type === 'updated' && 'is_active' in userData) {
    // Find the user's department
    const department = userData.department;
    if (department) {
      // Broadcast updated connected users for this department
      broadcastConnectedUsers(department);
    }
  }

  // Log success
  logger.info(`Broadcast complete for user ${userData.name} (${userData.id})`);
}

// Broadcast registration update to all clients
export function broadcastRegistrationUpdate(type: string, registrationData: any) {
  if (!ioInstance) return;

  ioInstance.emit('registration_update', {
    type,
    registration: registrationData
  });
}

// PRODUCTION-GRADE: Broadcast voucher update with infinite loop protection
export function broadcastVoucherUpdate(type: string, voucherData: any) {
  if (!ioInstance) {
    logger.error('Socket.IO instance not initialized');
    return;
  }

  try {
    const { id: voucherId, department, original_department } = voucherData;
    const timestamp = Date.now();

    // INFINITE LOOP PROTECTION: Check for duplicate events
    const eventKey = {
      type: `voucher_update_${type}`,
      entityId: voucherId,
      userId: department
    };

    if (!eventDeduplicator.shouldProcess(eventKey)) {
      logger.warn(`🚫 INFINITE LOOP PROTECTION: Blocked duplicate voucher update for ${voucherId}`);
      return;
    }

    // CIRCUIT BREAKER PROTECTION: Execute broadcast with circuit breaker
    circuitBreakerManager.execute('voucher_broadcast', async () => {
      // Log the broadcast attempt
      logger.info(`Broadcasting voucher update for ${voucherId} to department ${department} (original: ${original_department})`);

      // CROSS-DEPARTMENT REAL-TIME FIX: Broadcast to current department
      if (department && ioInstance) {
        ioInstance.to(`department:${department}`).emit('voucher_update', {
          type,
          voucher: voucherData,
          timestamp
        });
        logger.info(`📢 REAL-TIME: Broadcasted to current department: ${department}`);
      }

      // CROSS-DEPARTMENT REAL-TIME FIX: Also broadcast to original department if different
      if (original_department && original_department !== department && original_department !== 'AUDIT' && ioInstance) {
        ioInstance.to(`department:${original_department}`).emit('voucher_update', {
          type,
          voucher: voucherData,
          timestamp
        });
        logger.info(`📢 REAL-TIME: Broadcasted to original department: ${original_department}`);
      }

      // Always broadcast to audit and system admin rooms (but avoid double broadcast to AUDIT)
      if (department !== 'AUDIT' && ioInstance) {
        ioInstance.to('department:AUDIT').emit('voucher_update', {
          type,
          voucher: voucherData,
          timestamp
        });
      }

      if (ioInstance) {
        ioInstance.to('department:SYSTEM ADMIN').emit('voucher_update', {
          type,
          voucher: voucherData,
          timestamp
        });
      }

      // Mark event as processed
      eventDeduplicator.markProcessed(eventKey);

      // CRITICAL FIX: Force immediate tab refresh for dispatch events
      if ((type === 'dispatch_confirmed' || voucherData.auditDispatchedBy) && ioInstance) {
        logger.info(`🔄 REAL-TIME: Broadcasting immediate tab refresh for dispatch event`);
        ioInstance.emit('force_tab_refresh', {
          type: 'dispatch_confirmed',
          voucherId: voucherId,
          department: voucherData.originalDepartment,
          timestamp: Date.now()
        });
      }

      // Log success
      logger.info(`✅ REAL-TIME: Successfully broadcasted voucher update for ${voucherId} to all relevant departments`);
    }).catch(error => {
      logger.error('Circuit breaker blocked voucher broadcast:', error);
    });

  } catch (error) {
    logger.error('Error broadcasting voucher update:', error);
  }
}

// Broadcast batch update to all clients
export function broadcastBatchUpdate(type: string, batchData: any) {
  if (!ioInstance) return;

  ioInstance.emit('batch_update', {
    type,
    batch: batchData
  });
}

// Broadcast notification update to all clients
export function broadcastNotificationUpdate(type: string, notificationData: any) {
  if (!ioInstance) return;

  ioInstance.emit('notification_update', {
    type,
    notification: notificationData
  });
}

// REAL-TIME BATCH NOTIFICATION: Broadcast to Audit department
export function broadcastToAuditDepartment(event: string, data: any) {
  if (!ioInstance) {
    logger.warn('⚠️ Cannot broadcast to Audit - WebSocket not initialized');
    return;
  }

  try {
    // ENHANCED DEBUGGING: Check connected clients in AUDIT room
    const auditClients = Array.from(connectedClients.values()).filter(c => c.department === 'AUDIT');
    logger.info(`🔍 AUDIT BROADCAST DEBUG: Found ${auditClients.length} connected Audit clients`);

    if (auditClients.length > 0) {
      auditClients.forEach(client => {
        logger.info(`   - ${client.userName} (${client.userId}) - Socket: ${client.socket.id}`);
      });
    } else {
      logger.warn('⚠️ No Audit clients connected - broadcast will not reach anyone');
    }

    // Check if the room exists and has members
    const auditRoom = ioInstance.sockets.adapter.rooms.get('department:AUDIT');
    const roomSize = auditRoom ? auditRoom.size : 0;
    logger.info(`🏠 AUDIT ROOM DEBUG: Room 'department:AUDIT' has ${roomSize} members`);

    // Broadcast to Audit department room
    ioInstance.to('department:AUDIT').emit(event, data);
    logger.info(`📢 REAL-TIME: Broadcasted ${event} to Audit department (${roomSize} recipients):`, data);

    // Also try broadcasting to individual Audit sockets as fallback
    auditClients.forEach(client => {
      try {
        client.socket.emit(event, data);
        logger.info(`📡 FALLBACK: Direct emit to ${client.userName}`);
      } catch (socketError) {
        logger.error(`❌ Failed to emit to ${client.userName}:`, socketError);
      }
    });

  } catch (error) {
    logger.error('❌ Error broadcasting to Audit department:', error);
  }
}

// BIDIRECTIONAL FIX: Broadcast to specific department (for Audit → Department notifications)
export function broadcastToDepartment(department: string, event: string, data: any) {
  if (!ioInstance) {
    logger.warn(`⚠️ Cannot broadcast to ${department} - WebSocket not initialized`);
    return;
  }

  try {
    // ENHANCED DEBUGGING: Check connected clients in target department room
    const departmentClients = Array.from(connectedClients.values()).filter(c => c.department === department);
    logger.info(`🔍 ${department} BROADCAST DEBUG: Found ${departmentClients.length} connected ${department} clients`);

    if (departmentClients.length > 0) {
      departmentClients.forEach(client => {
        logger.info(`   - ${client.userName} (${client.userId}) - Socket: ${client.socket.id}`);
      });
    } else {
      logger.warn(`⚠️ No ${department} clients connected - broadcast will not reach anyone`);
    }

    // Check if the room exists and has members
    const departmentRoom = ioInstance.sockets.adapter.rooms.get(`department:${department}`);
    const roomSize = departmentRoom ? departmentRoom.size : 0;
    logger.info(`🏠 ${department} ROOM DEBUG: Room 'department:${department}' has ${roomSize} members`);

    // Broadcast to department room
    ioInstance.to(`department:${department}`).emit(event, data);
    logger.info(`📢 REAL-TIME: Broadcasted ${event} to ${department} department (${roomSize} recipients):`, data);

    // Also try broadcasting to individual department sockets as fallback
    departmentClients.forEach(client => {
      try {
        client.socket.emit(event, data);
        logger.info(`📡 FALLBACK: Direct emit to ${client.userName}`);
      } catch (socketError) {
        logger.error(`❌ Failed to emit to ${client.userName}:`, socketError);
      }
    });

  } catch (error) {
    logger.error(`❌ ${department} BROADCAST ERROR:`, error);
  }
}

// Generic data update broadcaster for any entity type
export function broadcastDataUpdate(entityType: string, actionType: string, data: any) {
  if (!ioInstance) return;

  const identifier = data.id || data.voucher_id || data._id || 'unknown';
  logger.info(`Broadcasting ${actionType} for ${entityType}: ${identifier}`);

  try {
    // Get all connected clients
    const clientCount = ioInstance.sockets.sockets.size;
    logger.info(`Broadcasting to ${clientCount} connected clients`);

    // SIMPLE: Emit to all clients with basic error handling
    ioInstance.emit('data_update', {
      entityType,
      actionType,
      data
    });

    // Log success
    logger.info(`Broadcast complete for ${entityType} ${identifier}`);

  } catch (error) {
    logger.error(`❌ Broadcast failed for ${entityType} ${identifier}:`, error);

    // SIMPLE RETRY: Try once more after brief delay
    setTimeout(() => {
      try {
        ioInstance?.emit('data_update', { entityType, actionType, data });
        logger.info(`✅ Broadcast retry successful for ${entityType} ${identifier}`);
      } catch (retryError) {
        logger.error(`❌ Broadcast retry failed for ${entityType} ${identifier}:`, retryError);
      }
    }, 1000);
  }
}

// Get the number of viewers for a resource
function getResourceViewerCount(resourceKey: string): number {
  let count = 0;
  for (const client of connectedClients.values()) {
    if (client.viewingResources.has(resourceKey)) {
      count++;
    }
  }
  return count;
}

// Get all users viewing a resource
function getResourceViewers(resourceKey: string): Array<{userId: string, userName: string, department: string}> {
  const viewers: Array<{userId: string, userName: string, department: string}> = [];
  for (const client of connectedClients.values()) {
    if (client.viewingResources.has(resourceKey)) {
      viewers.push({
        userId: client.userId,
        userName: client.userName,
        department: client.department
      });
    }
  }
  return viewers;
}

// Broadcast resource viewers to all clients
function broadcastResourceViewers(resourceKey: string) {
  if (!ioInstance) return;

  const viewers = getResourceViewers(resourceKey);
  const viewerCount = viewers.length;

  logger.info(`Broadcasting ${viewerCount} viewers for resource ${resourceKey}`);

  ioInstance.emit('resource_viewers', {
    resourceKey,
    viewers,
    viewerCount,
    timestamp: Date.now()
  });
}

// Broadcast connected users for a department
function broadcastConnectedUsers(department: string) {
  if (!ioInstance) return;

  const now = Date.now();

  // FIXED: Get unique users only (no duplicates)
  const uniqueUsers = new Map<string, any>();

  Array.from(connectedClients.values())
    .filter(client => client.department === department)
    .forEach(client => {
      const isActive = now - client.lastActivity < ACTIVITY_TIMEOUT;

      // Only keep the most recent connection for each user
      const existingUser = uniqueUsers.get(client.userId);
      if (!existingUser || client.lastActivity > existingUser.lastActivity) {
        uniqueUsers.set(client.userId, {
          id: client.userId,
          name: client.userName,
          department: client.department,
          isActive: isActive,
          lastActivity: client.lastActivity,
          // Check if user has any department locks
          isEditing: Array.from(departmentLocks.values()).some(lock => lock.ownerId === client.userId)
        });
      }
    });

  const departmentUsers = Array.from(uniqueUsers.values());

  // Log the users
  logger.info(`Broadcasting ${departmentUsers.length} connected users for department ${department}`);
  console.log('Department users:', departmentUsers.map(u => `${u.name} (${u.isActive ? 'active' : 'inactive'})`));

  // Emit to department-specific room
  ioInstance.to(`department:${department}`).emit('connected_users', {
    users: departmentUsers,
    department: department,
    timestamp: now
  });

  // Also emit to AUDIT and SYSTEM ADMIN rooms
  if (department !== 'AUDIT') {
    ioInstance.to('department:AUDIT').emit('connected_users', {
      users: departmentUsers,
      department: department,
      timestamp: now
    });
  }

  if (department !== 'SYSTEM ADMIN') {
    ioInstance.to('department:SYSTEM ADMIN').emit('connected_users', {
      users: departmentUsers,
      department: department,
      timestamp: now
    });
  }

  // Also emit to all clients for backward compatibility
  ioInstance.emit('connected_users', {
    users: departmentUsers,
    department: department,
    timestamp: now
  });

  // Also broadcast active department locks
  const activeDepartmentLocks = Array.from(departmentLocks.entries())
    .filter(([dept, lock]) => lock.ownerDepartment === department)
    .map(([dept, lock]) => {
      return {
        key: `department:${dept}`,
        userId: lock.ownerId,
        userName: lock.ownerName || 'Unknown',
        department: lock.ownerDepartment,
        expiresAt: lock.expiresAt,
        resourceType: 'department',
        resourceId: dept,
        // Calculate remaining time in seconds
        remainingTime: Math.max(0, Math.floor((lock.expiresAt - now) / 1000))
      };
    });

  if (ioInstance) {
    ioInstance.emit('department_locks', {
      locks: activeDepartmentLocks,
      department: department,
      timestamp: now
    });
  }
}

// ROBUSTNESS: Clean up stale connections and monitor health
function cleanupStaleConnections() {
  const now = Date.now();
  const staleThreshold = 5 * 60 * 1000; // 5 minutes without heartbeat
  const staleConnections: string[] = [];

  connectedClients.forEach((client, socketId) => {
    const timeSinceHeartbeat = now - (client.lastHeartbeat || client.lastActivity);

    // Check if connection is stale
    if (timeSinceHeartbeat > staleThreshold) {
      staleConnections.push(socketId);
      logger.warn(`🔍 Stale connection detected: ${client.userName} (${client.userId}) - ${Math.round(timeSinceHeartbeat/1000)}s since last heartbeat`);
    }
  });

  // Clean up stale connections
  staleConnections.forEach(socketId => {
    const client = connectedClients.get(socketId);
    if (client) {
      logger.info(`🧹 Cleaning up stale connection: ${client.userName} (${client.userId})`);

      // Release any department locks held by this user
      releaseUserDepartmentLocks(client.userId);

      // Remove from connected clients
      connectedClients.delete(socketId);

      // Disconnect the socket if still connected
      try {
        if (client.socket && client.socket.connected) {
          client.socket.disconnect(true);
        }
      } catch (error) {
        logger.error('❌ Error disconnecting stale socket:', error);
      }

      // PRODUCTION FIX: Only broadcast if user was recently active (not just stale connection)
      const wasRecentlyActive = (now - client.lastActivity) < 60000; // Active within 1 minute

      if (ioInstance && wasRecentlyActive) {
        ioInstance.to(`department:${client.department}`).emit('user_left', {
          userId: client.userId,
          userName: client.userName,
          department: client.department,
          timestamp: now,
          reason: 'connection_timeout'
        });
        logger.info(`📢 Broadcasting user_left for recently active user: ${client.userName} (${client.department})`);
      } else if (ioInstance) {
        logger.info(`🔇 Skipping user_left broadcast for stale connection: ${client.userName} (${client.department})`);
      }

      // Always update connected users list
      if (ioInstance) {
        broadcastConnectedUsers(client.department);
      }
    }
  });

  // Log cleanup results
  if (staleConnections.length > 0) {
    logger.info(`🧹 Cleaned up ${staleConnections.length} stale connections`);
  }

  // Log connection health summary
  const totalConnections = connectedClients.size;
  const healthyConnections = Array.from(connectedClients.values())
    .filter(client => (now - (client.lastHeartbeat || client.lastActivity)) < 60000).length;

  logger.debug(`📊 Connection Health: ${healthyConnections}/${totalConnections} healthy connections`);
}

// ROBUSTNESS: Comprehensive system health monitoring and recovery
function performSystemHealthCheck() {
  const now = Date.now();
  const healthReport = {
    timestamp: now,
    connections: {
      total: connectedClients.size,
      healthy: 0,
      stale: 0
    },
    locks: {
      total: departmentLocks.size,
      expired: 0,
      orphaned: 0
    },
    memory: process.memoryUsage(),
    uptime: process.uptime()
  };

  try {
    // Check connection health
    connectedClients.forEach(client => {
      const timeSinceHeartbeat = now - (client.lastHeartbeat || client.lastActivity);
      if (timeSinceHeartbeat < 60000) {
        healthReport.connections.healthy++;
      } else {
        healthReport.connections.stale++;
      }
    });

    // Check department lock health
    departmentLocks.forEach(lock => {
      if (lock.expiresAt <= now) {
        healthReport.locks.expired++;
      }

      const userConnected = Array.from(connectedClients.values())
        .some(client => client.userId === lock.ownerId);
      if (!userConnected) {
        healthReport.locks.orphaned++;
      }
    });

    // MONITORING: Log health summary
    logger.info(`🏥 System Health Check:`, {
      connections: `${healthReport.connections.healthy}/${healthReport.connections.total} healthy`,
      locks: `${healthReport.locks.total} total (${healthReport.locks.expired} expired, ${healthReport.locks.orphaned} orphaned)`,
      memory: `${Math.round(healthReport.memory.heapUsed / 1024 / 1024)}MB heap used`,
      uptime: `${Math.round(healthReport.uptime / 3600)}h`
    });

    // ALERTS: Warn about potential issues
    if (healthReport.connections.stale > healthReport.connections.total * 0.3) {
      logger.warn(`⚠️ High stale connection ratio: ${healthReport.connections.stale}/${healthReport.connections.total}`);
    }

    if (healthReport.locks.expired > 5) {
      logger.warn(`⚠️ High expired lock count: ${healthReport.locks.expired}`);
    }

    if (healthReport.memory.heapUsed > 500 * 1024 * 1024) { // 500MB
      logger.warn(`⚠️ High memory usage: ${Math.round(healthReport.memory.heapUsed / 1024 / 1024)}MB`);
    }

    // RECOVERY: Trigger cleanup if needed
    if (healthReport.locks.expired > 0 || healthReport.locks.orphaned > 0) {
      logger.info('🔧 Triggering cleanup due to problematic locks');
      cleanupExpiredDepartmentLocks();
    }

    if (healthReport.connections.stale > 0) {
      logger.info('🔧 Triggering connection cleanup due to stale connections');
      cleanupStaleConnections();
    }

  } catch (error) {
    logger.error('❌ Error during system health check:', error);
  }
}

// Release user locks specifically on logout (more aggressive cleanup)
function releaseUserLocksOnLogout(userId: string) {
  // Count department locks before cleanup
  const locksBeforeCleanup = Array.from(departmentLocks.values()).filter(lock => lock.ownerId === userId).length;

  // Use the existing department lock cleanup function
  releaseUserDepartmentLocks(userId);

  // Also cleanup any stale connected client entries for this user
  const staleConnections: string[] = [];
  for (const [socketId, client] of connectedClients.entries()) {
    if (client.userId === userId) {
      staleConnections.push(socketId);
    }
  }

  staleConnections.forEach(socketId => {
    const client = connectedClients.get(socketId);
    if (client) {
      logger.info(`🧹 Logout cleanup: Removing stale connection ${socketId} for user ${client.userName} (${userId})`);
      connectedClients.delete(socketId);

      // Broadcast updated connected users for the department
      broadcastConnectedUsers(client.department);
    }
  });

  // Log comprehensive cleanup results
  if (locksBeforeCleanup > 0 || staleConnections.length > 0) {
    logger.info(`🚪 Logout cleanup completed for user ${userId}: ${locksBeforeCleanup} locks released, ${staleConnections.length} stale connections removed`);
  }

  return locksBeforeCleanup;
}

// Export functions for admin use
export { clearAllDepartmentLocks, releaseUserLocksOnLogout };
