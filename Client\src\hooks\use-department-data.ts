
import { useAppStore } from '@/lib/store';
import { Department, Voucher } from '@/lib/types';
import { useEffect, useState } from 'react';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export function useDepartmentData(department?: Department, refreshTrigger: number = 0) {
  // ARCHITECTURAL FIX: Direct store subscription for real-time updates
  const allVouchers = useAppStore((state) => state.vouchers);
  const voucherBatches = useAppStore((state) => state.voucherBatches);
  const getVouchersForDepartment = useAppStore((state) => state.getVouchersForDepartment);
  const getPendingVouchersForDepartment = useAppStore((state) => state.getPendingVouchersForDepartment);

  // PRODUCTION FIX: Add store version for change detection
  const storeVersion = useAppStore((state) => state.version || 0);
  const lastUpdate = useAppStore((state) => state.lastUpdate || 0);
  const forceUpdate = useAppStore((state) => state.forceUpdate || 0);

  // FORCE RE-RENDER: Local state to trigger component updates
  const [renderTrigger, setRenderTrigger] = useState(0);

  const [data, setData] = useState({
    vouchers: [] as Voucher[],
    pendingVouchers: [] as Voucher[],
    pendingSubmissionVouchers: [] as Voucher[],
    processingVouchers: [] as Voucher[],
    certifiedVouchers: [] as Voucher[],
    rejectedVouchers: [] as Voucher[],
    returnedVouchers: [] as Voucher[],
    vouchersToReceive: [] as Voucher[],
    departmentBatches: [] as any[],
    batchesArray: [] as any[]
  });

  // REMOVED: Duplicate declarations - already defined above

  // REAL-TIME VOUCHER CREATION FIX: Listen for voucher creation events
  useEffect(() => {
    const handleVoucherCreated = (event: CustomEvent) => {
      const eventType = event.detail?.type;
      if (eventType === 'created' || eventType === 'voucher_created') {
        console.log(`🔄 REAL-TIME CREATION: useDepartmentData received voucher creation event for ${department}`);
        // Force re-render by updating render trigger
        setRenderTrigger(prev => prev + 1);
      }
    };

    const handleVoucherListRefresh = (event: CustomEvent) => {
      if (event.detail?.department === department || !event.detail?.department) {
        console.log(`🔄 REAL-TIME CREATION: useDepartmentData received list refresh event for ${department}`);
        setRenderTrigger(prev => prev + 1);
      }
    };

    window.addEventListener('voucherUpdated', handleVoucherCreated as EventListener);
    window.addEventListener('voucherListRefresh', handleVoucherListRefresh as EventListener);

    return () => {
      window.removeEventListener('voucherUpdated', handleVoucherCreated as EventListener);
      window.removeEventListener('voucherListRefresh', handleVoucherListRefresh as EventListener);
    };
  }, [department]);

  // ARCHITECTURAL FIX: React to store changes in real-time
  useEffect(() => {
    if (!department) {
      // Ensure we always have valid data even when department is undefined
      setData({
        vouchers: [],
        pendingVouchers: [],
        pendingSubmissionVouchers: [],
        processingVouchers: [],
        certifiedVouchers: [],
        rejectedVouchers: [],
        returnedVouchers: [],
        vouchersToReceive: [],
        departmentBatches: [],
        batchesArray: []
      });
      return;
    }



    // FORCE RE-RENDER: Trigger local state update
    setRenderTrigger(prev => prev + 1);

    // SIMPLE APPROACH: Get all vouchers that belong to this department (current or original)
    // CRITICAL FIX: Exclude vouchers that have been offset by audit to prevent duplication
    const allRelevantVouchers = allVouchers.filter(v =>
      (v.department === department || v.originalDepartment === department) &&
      v.status !== 'OFFSET_BY_AUDIT' && // Exclude offset vouchers
      !v.deleted // Exclude deleted vouchers
    );



    // SIMPLE FILTERING: Clear business rules

    // PENDING: Vouchers still in this department that haven't been sent to audit AND have pending status
    const pendingVouchers = allRelevantVouchers.filter(v =>
      v.department === department &&
      !v.sentToAudit &&
      (v.status === VOUCHER_STATUSES.PENDING || v.status === VOUCHER_STATUSES.PENDING_SUBMISSION)
    );

    // For backward compatibility
    const pendingSubmissionVouchers = pendingVouchers;

    // PROCESSING: Vouchers that originated from this department and were sent to audit
    // CRITICAL FIX: Exclude vouchers with final statuses (they should appear in their specific tabs)
    const processingVouchers = allRelevantVouchers.filter(v =>
      v.originalDepartment === department &&
      v.sentToAudit === true &&
      v.status !== VOUCHER_STATUSES.VOUCHER_CERTIFIED &&
      v.status !== VOUCHER_STATUSES.VOUCHER_REJECTED &&
      v.status !== VOUCHER_STATUSES.VOUCHER_RETURNED
    );



    // CERTIFIED: Vouchers that were certified by audit and returned to department
    const certifiedVouchers = allRelevantVouchers.filter(v =>
      v.originalDepartment === department &&
      v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED &&
      !v.deleted // Exclude deleted vouchers
    );

    // REJECTED: Vouchers that were rejected by audit and returned to department
    // CRITICAL FIX: Only show rejected vouchers that have been received by the department
    // Rejected vouchers still in AUDIT (for dispatch) should not appear here
    const rejectedVouchers = allRelevantVouchers.filter(v =>
      v.originalDepartment === department &&
      v.status === VOUCHER_STATUSES.VOUCHER_REJECTED &&
      v.department === department && // Must be back in department, not still in AUDIT
      !v.deleted // Exclude deleted vouchers
    );

    // RETURNED: Vouchers that were returned by audit for rework
    const returnedVouchers = allRelevantVouchers.filter(v =>
      v.originalDepartment === department &&
      v.status === VOUCHER_STATUSES.VOUCHER_RETURNED &&
      !v.deleted // Exclude deleted vouchers
    );

    // Get batches for the current department
    const departmentBatches = voucherBatches.filter(batch =>
      batch.department === department
    );

    // CRITICAL FIX: Include batches from audit to this department
    // These are batches with from_audit=true that need to be received by this department
    const batchesFromAudit = voucherBatches.filter(batch => {
      // FIX: Handle database returning numbers instead of booleans
      const isFromAudit = Boolean(batch.fromAudit); // Convert 1/0 to true/false
      const isNotReceived = !Boolean(batch.received); // Convert 1/0 to true/false
      return batch.department === department && isFromAudit && isNotReceived;
    });

    setData({
      vouchers: allRelevantVouchers,
      pendingVouchers,
      pendingSubmissionVouchers,
      processingVouchers,
      certifiedVouchers,
      rejectedVouchers,
      returnedVouchers,
      vouchersToReceive: batchesFromAudit.flatMap(batch => batch.vouchers || []), // Vouchers from audit batches that need to be received
      departmentBatches,
      batchesArray: batchesFromAudit // CRITICAL FIX: Show batches from audit that need to be received
    });

  }, [department, allVouchers, voucherBatches, renderTrigger, storeVersion, lastUpdate, forceUpdate, refreshTrigger]);

  return data;
}
