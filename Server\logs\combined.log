{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🎯 Using forced port: 8080","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔧 Setting up Socket.IO middleware and handlers...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Starting workflow state migration...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"No vouchers need workflow state migration","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Users already exist (10 users found)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔄 Initializing workflow service...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Initializing workflow service...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔄 Initializing file storage...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"File storage initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ File storage initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🌐 Environment: DEVELOPMENT","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📊 Process ID: 14580","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"New database connection established: 673","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database connection test successful","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database 'vms_production' verified","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Essential database tables verified","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database setup verification complete","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Health checks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database Manager initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Session cleanup scheduled (every 10 minutes)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🛑 Automated backup scheduler stopped","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🕒 Starting automated backup scheduler (every 24 hours)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Automated backup scheduler started (daily backups)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🖥️  VMS System: http://************:8080","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🖥️  VMS System: http://10.240.224.232:8080","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🔌 WebSocket: http://10.240.224.232:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   💾 Memory: 65MB RSS","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🔄 Uptime: 1s","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1754126104724,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082871126","service":"vms-server","timestamp":"2025-08-02T09:15:05.421Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082871126 \u001b[32m200\u001b[0m 7.394 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082876139","service":"vms-server","timestamp":"2025-08-02T09:15:05.454Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082876139 \u001b[32m200\u001b[0m 3.356 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"🔌 WebSocket connection established: LAN User (lan-user) from GUEST [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"🚀 Setting up authenticated connection for LAN User (lan-user) from GUEST","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"User LAN User (lan-user) joined room: department:GUEST","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"User LAN User (lan-user) joined personal room: user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"Updated existing session with socket info for LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"Total connected clients: 1, 1 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"Socket BUABzViaXIrrF6hwAAAF is in rooms: BUABzViaXIrrF6hwAAAF, department:GUEST, user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082881126","service":"vms-server","timestamp":"2025-08-02T09:15:09.867Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082881126 \u001b[32m200\u001b[0m 19.946 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082886125","service":"vms-server","timestamp":"2025-08-02T09:15:14.892Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082886125 \u001b[32m200\u001b[0m 4.840 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:14"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082891136","service":"vms-server","timestamp":"2025-08-02T09:15:19.868Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082891136 \u001b[32m200\u001b[0m 5.183 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082896126","service":"vms-server","timestamp":"2025-08-02T09:15:24.857Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082896126 \u001b[32m200\u001b[0m 4.165 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082901126","service":"vms-server","timestamp":"2025-08-02T09:15:29.856Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082901126 \u001b[32m200\u001b[0m 4.490 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:29"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"level":"info","message":"✅ No duplicate sessions found","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"level":"info","message":"✅ All users have unique sessions (2 unique active users)","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"level":"info","message":"✅ Initial session cleanup completed","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082906136","service":"vms-server","timestamp":"2025-08-02T09:15:34.867Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082906136 \u001b[32m200\u001b[0m 2.994 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082911128","service":"vms-server","timestamp":"2025-08-02T09:15:39.910Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082911128 \u001b[32m200\u001b[0m 6.515 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082916136","service":"vms-server","timestamp":"2025-08-02T09:15:44.867Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082916136 \u001b[32m200\u001b[0m 2.737 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:15:44.904Z"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User test-user-123 (browser_close_test) at 2025-08-02T09:15:44.863Z","service":"vms-server","timestamp":"2025-08-02 09:15:44"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'logout_reason' in 'field list'","service":"vms-server","sql":"UPDATE active_sessions SET is_active = FALSE, logout_reason = ?, session_end = NOW() WHERE id = ?","sqlMessage":"Unknown column 'logout_reason' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'logout_reason' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at query (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\database\\db.js:777:38)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:239:37\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-08-02 09:15:44"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'logout_reason' in 'field list'","service":"vms-server","sql":"UPDATE active_sessions SET is_active = FALSE, logout_reason = ?, session_end = NOW() WHERE id = ?","sqlMessage":"Unknown column 'logout_reason' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'logout_reason' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at query (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\database\\db.js:777:38)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:239:37\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-08-02 09:15:44"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 16.979 ms - 74\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:44"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082921126","service":"vms-server","timestamp":"2025-08-02T09:15:49.896Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082921126 \u001b[32m200\u001b[0m 6.292 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082926137","service":"vms-server","timestamp":"2025-08-02T09:15:54.868Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082926137 \u001b[32m200\u001b[0m 2.859 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:54"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082931126","service":"vms-server","timestamp":"2025-08-02T09:15:59.860Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:59"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082931126 \u001b[32m200\u001b[0m 3.877 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:59"}
{"level":"info","message":"🔍 Checking for year rollover...","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"🗓️ Year rollover monitoring started - checking daily","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"📅 Current date: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"📊 Calendar year: 2025","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"📈 Calculated fiscal year: 2025","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"⚙️ Configured fiscal year: 2025","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082936133","service":"vms-server","timestamp":"2025-08-02T09:16:04.867Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082936133 \u001b[32m200\u001b[0m 3.234 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082941125","service":"vms-server","timestamp":"2025-08-02T09:16:09.864Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082941125 \u001b[32m200\u001b[0m 3.283 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082946140","service":"vms-server","timestamp":"2025-08-02T09:16:14.890Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082946140 \u001b[32m200\u001b[0m 2.695 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:14"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082951125","service":"vms-server","timestamp":"2025-08-02T09:16:19.853Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082951125 \u001b[32m200\u001b[0m 5.348 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082956125","service":"vms-server","timestamp":"2025-08-02T09:16:24.898Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082956125 \u001b[32m200\u001b[0m 3.311 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082961129","service":"vms-server","timestamp":"2025-08-02T09:16:29.859Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082961129 \u001b[32m200\u001b[0m 2.887 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:29"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082966127","service":"vms-server","timestamp":"2025-08-02T09:16:34.854Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082966127 \u001b[32m200\u001b[0m 3.366 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082971127","service":"vms-server","timestamp":"2025-08-02T09:16:39.852Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082971127 \u001b[32m200\u001b[0m 5.272 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082976127","service":"vms-server","timestamp":"2025-08-02T09:16:44.863Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082976127 \u001b[32m200\u001b[0m 6.080 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:44"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082981128","service":"vms-server","timestamp":"2025-08-02T09:16:49.894Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082981128 \u001b[32m200\u001b[0m 3.465 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082986133","service":"vms-server","timestamp":"2025-08-02T09:16:54.863Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082986133 \u001b[32m200\u001b[0m 6.061 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:54"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082991125","service":"vms-server","timestamp":"2025-08-02T09:16:59.863Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:59"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082991125 \u001b[32m200\u001b[0m 3.337 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:59"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082996131","service":"vms-server","timestamp":"2025-08-02T09:17:04.859Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082996131 \u001b[32m200\u001b[0m 2.781 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083001136","service":"vms-server","timestamp":"2025-08-02T09:17:09.862Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083001136 \u001b[32m200\u001b[0m 3.675 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083006140","service":"vms-server","timestamp":"2025-08-02T09:17:14.867Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083006140 \u001b[32m200\u001b[0m 4.955 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:14"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:17:18.921Z"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User test-user-123 (browser_close_test) at 2025-08-02T09:17:18.896Z","service":"vms-server","timestamp":"2025-08-02 09:17:18"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: Session test-session-456 deactivated, locks released","service":"vms-server","timestamp":"2025-08-02 09:17:18"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 4.559 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:18"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083011125","service":"vms-server","timestamp":"2025-08-02T09:17:19.854Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083011125 \u001b[32m200\u001b[0m 3.209 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083016140","service":"vms-server","timestamp":"2025-08-02T09:17:24.912Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083016140 \u001b[32m200\u001b[0m 13.640 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083021130","service":"vms-server","timestamp":"2025-08-02T09:17:29.855Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083021130 \u001b[32m200\u001b[0m 2.702 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:29"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083026130","service":"vms-server","timestamp":"2025-08-02T09:17:34.894Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083026130 \u001b[32m200\u001b[0m 3.473 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083031130","service":"vms-server","timestamp":"2025-08-02T09:17:39.856Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083031130 \u001b[32m200\u001b[0m 4.164 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083036129","service":"vms-server","timestamp":"2025-08-02T09:17:44.925Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083036129 \u001b[32m200\u001b[0m 5.277 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:44"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083041132","service":"vms-server","timestamp":"2025-08-02T09:17:49.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083041132 \u001b[32m200\u001b[0m 13.707 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083046131","service":"vms-server","timestamp":"2025-08-02T09:17:54.855Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083046131 \u001b[32m200\u001b[0m 3.366 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:54"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083051132","service":"vms-server","timestamp":"2025-08-02T09:17:59.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:59"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083051132 \u001b[32m200\u001b[0m 3.266 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:59"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083056133","service":"vms-server","timestamp":"2025-08-02T09:18:04.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083056133 \u001b[32m200\u001b[0m 2.981 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083061132","service":"vms-server","timestamp":"2025-08-02T09:18:09.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083061132 \u001b[32m200\u001b[0m 2.745 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083066130","service":"vms-server","timestamp":"2025-08-02T09:18:14.854Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083066130 \u001b[32m200\u001b[0m 2.713 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:14"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083071132","service":"vms-server","timestamp":"2025-08-02T09:18:19.857Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083071132 \u001b[32m200\u001b[0m 2.624 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083076132","service":"vms-server","timestamp":"2025-08-02T09:18:24.856Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083076132 \u001b[32m200\u001b[0m 2.673 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083081134","service":"vms-server","timestamp":"2025-08-02T09:18:29.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083081134 \u001b[32m200\u001b[0m 2.800 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:29"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083086134","service":"vms-server","timestamp":"2025-08-02T09:18:34.864Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083086134 \u001b[32m200\u001b[0m 3.089 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083091129","service":"vms-server","timestamp":"2025-08-02T09:18:39.862Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083091129 \u001b[32m200\u001b[0m 2.677 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083096138","service":"vms-server","timestamp":"2025-08-02T09:18:44.864Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083096138 \u001b[32m200\u001b[0m 5.542 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:44"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083101139","service":"vms-server","timestamp":"2025-08-02T09:18:49.862Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083101139 \u001b[32m200\u001b[0m 2.711 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083106138","service":"vms-server","timestamp":"2025-08-02T09:18:54.918Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083106138 \u001b[32m200\u001b[0m 2.715 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:54"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083111127","service":"vms-server","timestamp":"2025-08-02T09:18:59.850Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:59"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083111127 \u001b[32m200\u001b[0m 2.846 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:59"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083116129","service":"vms-server","timestamp":"2025-08-02T09:19:04.852Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083116129 \u001b[32m200\u001b[0m 5.386 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083121132","service":"vms-server","timestamp":"2025-08-02T09:19:09.854Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083121132 \u001b[32m200\u001b[0m 2.721 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083126126","service":"vms-server","timestamp":"2025-08-02T09:19:14.848Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083126126 \u001b[32m200\u001b[0m 3.176 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:14"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083131134","service":"vms-server","timestamp":"2025-08-02T09:19:19.863Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083131134 \u001b[32m200\u001b[0m 2.708 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083136135","service":"vms-server","timestamp":"2025-08-02T09:19:24.857Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083136135 \u001b[32m200\u001b[0m 5.629 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083141135","service":"vms-server","timestamp":"2025-08-02T09:19:29.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083141135 \u001b[32m200\u001b[0m 6.037 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:29"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083146135","service":"vms-server","timestamp":"2025-08-02T09:19:34.855Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083146135 \u001b[32m200\u001b[0m 2.856 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083151130","service":"vms-server","timestamp":"2025-08-02T09:19:39.851Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083151130 \u001b[32m200\u001b[0m 2.986 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083156129","service":"vms-server","timestamp":"2025-08-02T09:19:45.069Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:45"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083156129 \u001b[32m200\u001b[0m 5.453 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:45"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083161124","service":"vms-server","timestamp":"2025-08-02T09:19:49.844Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083161124 \u001b[32m200\u001b[0m 2.486 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083166125","service":"vms-server","timestamp":"2025-08-02T09:19:54.848Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083166125 \u001b[32m200\u001b[0m 4.591 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:54"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083171125","service":"vms-server","timestamp":"2025-08-02T09:19:59.847Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:59"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083171125 \u001b[32m200\u001b[0m 5.282 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:59"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"level":"info","message":"🔄 Starting automated backup...","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083176126","service":"vms-server","timestamp":"2025-08-02T09:20:04.846Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083176126 \u001b[32m200\u001b[0m 13.363 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"level":"info","message":"🗑️ Cleaned up old backup: vms_auto_backup_2025-08-01T19-59-30-631Z.sql","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"level":"info","message":"✅ Backup cleanup completed: removed 1 old backups","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"level":"info","message":"✅ Automated backup completed: vms_auto_backup_2025-08-02T09-20-04-703Z.sql (122769 bytes)","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"connections":"1/1 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-02 09:20:05","uptime":"0h"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083181126","service":"vms-server","timestamp":"2025-08-02T09:20:09.853Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:20:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083181126 \u001b[32m200\u001b[0m 3.304 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:20:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083186126","service":"vms-server","timestamp":"2025-08-02T09:20:14.847Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:20:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083186126 \u001b[32m200\u001b[0m 2.930 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:20:14"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083191131","service":"vms-server","timestamp":"2025-08-02T09:20:19.916Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:20:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083191131 \u001b[32m200\u001b[0m 2.668 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:20:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083196132","service":"vms-server","timestamp":"2025-08-02T09:20:24.852Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:20:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083196132 \u001b[32m200\u001b[0m 3.174 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:20:24"}
