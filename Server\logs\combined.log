{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🎯 Using forced port: 8080","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔧 Setting up Socket.IO middleware and handlers...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Starting workflow state migration...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"No vouchers need workflow state migration","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Users already exist (10 users found)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔄 Initializing workflow service...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Initializing workflow service...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔄 Initializing file storage...","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"File storage initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ File storage initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🌐 Environment: DEVELOPMENT","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📊 Process ID: 14580","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"New database connection established: 673","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database connection test successful","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database 'vms_production' verified","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Essential database tables verified","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database setup verification complete","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Health checks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"Database Manager initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Session cleanup scheduled (every 10 minutes)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🛑 Automated backup scheduler stopped","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🕒 Starting automated backup scheduler (every 24 hours)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"✅ Automated backup scheduler started (daily backups)","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🖥️  VMS System: http://************:8080","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🖥️  VMS System: http://10.240.224.232:8080","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🔌 WebSocket: http://10.240.224.232:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   💾 Memory: 65MB RSS","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"   🔄 Uptime: 1s","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1754126104724,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-08-02 09:15:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082871126","service":"vms-server","timestamp":"2025-08-02T09:15:05.421Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082871126 \u001b[32m200\u001b[0m 7.394 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082876139","service":"vms-server","timestamp":"2025-08-02T09:15:05.454Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082876139 \u001b[32m200\u001b[0m 3.356 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"🔌 WebSocket connection established: LAN User (lan-user) from GUEST [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"🚀 Setting up authenticated connection for LAN User (lan-user) from GUEST","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"User LAN User (lan-user) joined room: department:GUEST","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"User LAN User (lan-user) joined personal room: user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"Updated existing session with socket info for LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"Total connected clients: 1, 1 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"level":"info","message":"Socket BUABzViaXIrrF6hwAAAF is in rooms: BUABzViaXIrrF6hwAAAF, department:GUEST, user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:15:05"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082881126","service":"vms-server","timestamp":"2025-08-02T09:15:09.867Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082881126 \u001b[32m200\u001b[0m 19.946 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082886125","service":"vms-server","timestamp":"2025-08-02T09:15:14.892Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082886125 \u001b[32m200\u001b[0m 4.840 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:14"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082891136","service":"vms-server","timestamp":"2025-08-02T09:15:19.868Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082891136 \u001b[32m200\u001b[0m 5.183 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082896126","service":"vms-server","timestamp":"2025-08-02T09:15:24.857Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082896126 \u001b[32m200\u001b[0m 4.165 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082901126","service":"vms-server","timestamp":"2025-08-02T09:15:29.856Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082901126 \u001b[32m200\u001b[0m 4.490 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:29"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"level":"info","message":"✅ No duplicate sessions found","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"level":"info","message":"✅ All users have unique sessions (2 unique active users)","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"level":"info","message":"✅ Initial session cleanup completed","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082906136","service":"vms-server","timestamp":"2025-08-02T09:15:34.867Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082906136 \u001b[32m200\u001b[0m 2.994 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082911128","service":"vms-server","timestamp":"2025-08-02T09:15:39.910Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082911128 \u001b[32m200\u001b[0m 6.515 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082916136","service":"vms-server","timestamp":"2025-08-02T09:15:44.867Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082916136 \u001b[32m200\u001b[0m 2.737 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:15:44.904Z"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User test-user-123 (browser_close_test) at 2025-08-02T09:15:44.863Z","service":"vms-server","timestamp":"2025-08-02 09:15:44"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'logout_reason' in 'field list'","service":"vms-server","sql":"UPDATE active_sessions SET is_active = FALSE, logout_reason = ?, session_end = NOW() WHERE id = ?","sqlMessage":"Unknown column 'logout_reason' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'logout_reason' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at query (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\database\\db.js:777:38)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:239:37\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-08-02 09:15:44"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'logout_reason' in 'field list'","service":"vms-server","sql":"UPDATE active_sessions SET is_active = FALSE, logout_reason = ?, session_end = NOW() WHERE id = ?","sqlMessage":"Unknown column 'logout_reason' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'logout_reason' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at query (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\database\\db.js:777:38)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:239:37\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-08-02 09:15:44"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 16.979 ms - 74\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:44"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082921126","service":"vms-server","timestamp":"2025-08-02T09:15:49.896Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082921126 \u001b[32m200\u001b[0m 6.292 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082926137","service":"vms-server","timestamp":"2025-08-02T09:15:54.868Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082926137 \u001b[32m200\u001b[0m 2.859 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:54"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082931126","service":"vms-server","timestamp":"2025-08-02T09:15:59.860Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:15:59"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082931126 \u001b[32m200\u001b[0m 3.877 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:15:59"}
{"level":"info","message":"🔍 Checking for year rollover...","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"🗓️ Year rollover monitoring started - checking daily","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"📅 Current date: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"📊 Calendar year: 2025","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"📈 Calculated fiscal year: 2025","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"⚙️ Configured fiscal year: 2025","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082936133","service":"vms-server","timestamp":"2025-08-02T09:16:04.867Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082936133 \u001b[32m200\u001b[0m 3.234 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082941125","service":"vms-server","timestamp":"2025-08-02T09:16:09.864Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082941125 \u001b[32m200\u001b[0m 3.283 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082946140","service":"vms-server","timestamp":"2025-08-02T09:16:14.890Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082946140 \u001b[32m200\u001b[0m 2.695 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:14"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082951125","service":"vms-server","timestamp":"2025-08-02T09:16:19.853Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082951125 \u001b[32m200\u001b[0m 5.348 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082956125","service":"vms-server","timestamp":"2025-08-02T09:16:24.898Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082956125 \u001b[32m200\u001b[0m 3.311 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082961129","service":"vms-server","timestamp":"2025-08-02T09:16:29.859Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082961129 \u001b[32m200\u001b[0m 2.887 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:29"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082966127","service":"vms-server","timestamp":"2025-08-02T09:16:34.854Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082966127 \u001b[32m200\u001b[0m 3.366 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082971127","service":"vms-server","timestamp":"2025-08-02T09:16:39.852Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082971127 \u001b[32m200\u001b[0m 5.272 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082976127","service":"vms-server","timestamp":"2025-08-02T09:16:44.863Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082976127 \u001b[32m200\u001b[0m 6.080 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:44"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082981128","service":"vms-server","timestamp":"2025-08-02T09:16:49.894Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082981128 \u001b[32m200\u001b[0m 3.465 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082986133","service":"vms-server","timestamp":"2025-08-02T09:16:54.863Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082986133 \u001b[32m200\u001b[0m 6.061 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:54"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082991125","service":"vms-server","timestamp":"2025-08-02T09:16:59.863Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:16:59"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082991125 \u001b[32m200\u001b[0m 3.337 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:16:59"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754082996131","service":"vms-server","timestamp":"2025-08-02T09:17:04.859Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754082996131 \u001b[32m200\u001b[0m 2.781 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083001136","service":"vms-server","timestamp":"2025-08-02T09:17:09.862Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083001136 \u001b[32m200\u001b[0m 3.675 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083006140","service":"vms-server","timestamp":"2025-08-02T09:17:14.867Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083006140 \u001b[32m200\u001b[0m 4.955 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:14"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:17:18.921Z"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User test-user-123 (browser_close_test) at 2025-08-02T09:17:18.896Z","service":"vms-server","timestamp":"2025-08-02 09:17:18"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: Session test-session-456 deactivated, locks released","service":"vms-server","timestamp":"2025-08-02 09:17:18"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 4.559 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:18"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083011125","service":"vms-server","timestamp":"2025-08-02T09:17:19.854Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083011125 \u001b[32m200\u001b[0m 3.209 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083016140","service":"vms-server","timestamp":"2025-08-02T09:17:24.912Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083016140 \u001b[32m200\u001b[0m 13.640 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083021130","service":"vms-server","timestamp":"2025-08-02T09:17:29.855Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083021130 \u001b[32m200\u001b[0m 2.702 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:29"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083026130","service":"vms-server","timestamp":"2025-08-02T09:17:34.894Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083026130 \u001b[32m200\u001b[0m 3.473 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083031130","service":"vms-server","timestamp":"2025-08-02T09:17:39.856Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083031130 \u001b[32m200\u001b[0m 4.164 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083036129","service":"vms-server","timestamp":"2025-08-02T09:17:44.925Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083036129 \u001b[32m200\u001b[0m 5.277 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:44"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083041132","service":"vms-server","timestamp":"2025-08-02T09:17:49.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083041132 \u001b[32m200\u001b[0m 13.707 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083046131","service":"vms-server","timestamp":"2025-08-02T09:17:54.855Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083046131 \u001b[32m200\u001b[0m 3.366 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:54"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083051132","service":"vms-server","timestamp":"2025-08-02T09:17:59.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:17:59"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083051132 \u001b[32m200\u001b[0m 3.266 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:17:59"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083056133","service":"vms-server","timestamp":"2025-08-02T09:18:04.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083056133 \u001b[32m200\u001b[0m 2.981 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083061132","service":"vms-server","timestamp":"2025-08-02T09:18:09.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083061132 \u001b[32m200\u001b[0m 2.745 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083066130","service":"vms-server","timestamp":"2025-08-02T09:18:14.854Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083066130 \u001b[32m200\u001b[0m 2.713 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:14"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083071132","service":"vms-server","timestamp":"2025-08-02T09:18:19.857Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083071132 \u001b[32m200\u001b[0m 2.624 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083076132","service":"vms-server","timestamp":"2025-08-02T09:18:24.856Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083076132 \u001b[32m200\u001b[0m 2.673 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083081134","service":"vms-server","timestamp":"2025-08-02T09:18:29.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083081134 \u001b[32m200\u001b[0m 2.800 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:29"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083086134","service":"vms-server","timestamp":"2025-08-02T09:18:34.864Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083086134 \u001b[32m200\u001b[0m 3.089 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083091129","service":"vms-server","timestamp":"2025-08-02T09:18:39.862Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083091129 \u001b[32m200\u001b[0m 2.677 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083096138","service":"vms-server","timestamp":"2025-08-02T09:18:44.864Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083096138 \u001b[32m200\u001b[0m 5.542 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:44"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083101139","service":"vms-server","timestamp":"2025-08-02T09:18:49.862Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083101139 \u001b[32m200\u001b[0m 2.711 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083106138","service":"vms-server","timestamp":"2025-08-02T09:18:54.918Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083106138 \u001b[32m200\u001b[0m 2.715 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:54"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083111127","service":"vms-server","timestamp":"2025-08-02T09:18:59.850Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:18:59"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083111127 \u001b[32m200\u001b[0m 2.846 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:18:59"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083116129","service":"vms-server","timestamp":"2025-08-02T09:19:04.852Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083116129 \u001b[32m200\u001b[0m 5.386 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083121132","service":"vms-server","timestamp":"2025-08-02T09:19:09.854Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083121132 \u001b[32m200\u001b[0m 2.721 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083126126","service":"vms-server","timestamp":"2025-08-02T09:19:14.848Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083126126 \u001b[32m200\u001b[0m 3.176 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:14"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083131134","service":"vms-server","timestamp":"2025-08-02T09:19:19.863Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083131134 \u001b[32m200\u001b[0m 2.708 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083136135","service":"vms-server","timestamp":"2025-08-02T09:19:24.857Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083136135 \u001b[32m200\u001b[0m 5.629 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083141135","service":"vms-server","timestamp":"2025-08-02T09:19:29.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083141135 \u001b[32m200\u001b[0m 6.037 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:29"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083146135","service":"vms-server","timestamp":"2025-08-02T09:19:34.855Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083146135 \u001b[32m200\u001b[0m 2.856 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083151130","service":"vms-server","timestamp":"2025-08-02T09:19:39.851Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083151130 \u001b[32m200\u001b[0m 2.986 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083156129","service":"vms-server","timestamp":"2025-08-02T09:19:45.069Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:45"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083156129 \u001b[32m200\u001b[0m 5.453 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:45"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083161124","service":"vms-server","timestamp":"2025-08-02T09:19:49.844Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083161124 \u001b[32m200\u001b[0m 2.486 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083166125","service":"vms-server","timestamp":"2025-08-02T09:19:54.848Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083166125 \u001b[32m200\u001b[0m 4.591 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:54"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083171125","service":"vms-server","timestamp":"2025-08-02T09:19:59.847Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:19:59"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083171125 \u001b[32m200\u001b[0m 5.282 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:19:59"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"level":"info","message":"🔄 Starting automated backup...","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083176126","service":"vms-server","timestamp":"2025-08-02T09:20:04.846Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083176126 \u001b[32m200\u001b[0m 13.363 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"level":"info","message":"🗑️ Cleaned up old backup: vms_auto_backup_2025-08-01T19-59-30-631Z.sql","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"level":"info","message":"✅ Backup cleanup completed: removed 1 old backups","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"level":"info","message":"✅ Automated backup completed: vms_auto_backup_2025-08-02T09-20-04-703Z.sql (122769 bytes)","service":"vms-server","timestamp":"2025-08-02 09:20:04"}
{"connections":"1/1 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-02 09:20:05","uptime":"0h"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083181126","service":"vms-server","timestamp":"2025-08-02T09:20:09.853Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:20:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083181126 \u001b[32m200\u001b[0m 3.304 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:20:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083186126","service":"vms-server","timestamp":"2025-08-02T09:20:14.847Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:20:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083186126 \u001b[32m200\u001b[0m 2.930 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:20:14"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083191131","service":"vms-server","timestamp":"2025-08-02T09:20:19.916Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:20:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083191131 \u001b[32m200\u001b[0m 2.668 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:20:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083196132","service":"vms-server","timestamp":"2025-08-02T09:20:24.852Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:20:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083196132 \u001b[32m200\u001b[0m 3.174 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:20:24"}
{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🎯 Using forced port: 8080","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🔧 Setting up Socket.IO middleware and handlers...","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Starting workflow state migration...","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"No vouchers need workflow state migration","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Users already exist (10 users found)","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🔄 Initializing workflow service...","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Initializing workflow service...","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🔄 Initializing file storage...","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"File storage initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"✅ File storage initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🌐 Environment: DEVELOPMENT","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"📊 Process ID: 35448","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"New database connection established: 681","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Database connection test successful","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Database 'vms_production' verified","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Essential database tables verified","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Database setup verification complete","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Health checks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"Database Manager initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"✅ Session cleanup scheduled (every 10 minutes)","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🛑 Automated backup scheduler stopped","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🕒 Starting automated backup scheduler (every 24 hours)","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"✅ Automated backup scheduler started (daily backups)","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"   🖥️  VMS System: http://************:8080","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"   🖥️  VMS System: http://10.240.224.232:8080","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"   🔌 WebSocket: http://10.240.224.232:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"   💾 Memory: 65MB RSS","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"   🔄 Uptime: 1s","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1754126604933,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-08-02 09:23:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083281136","service":"vms-server","timestamp":"2025-08-02T09:23:25.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083286137","service":"vms-server","timestamp":"2025-08-02T09:23:25.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083281136 \u001b[32m200\u001b[0m 15.747 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083286137 \u001b[32m200\u001b[0m 15.232 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083291134","service":"vms-server","timestamp":"2025-08-02T09:23:25.030Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083291134 \u001b[32m200\u001b[0m 3.134 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083296136","service":"vms-server","timestamp":"2025-08-02T09:23:25.035Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083296136 \u001b[32m200\u001b[0m 4.615 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083301129","service":"vms-server","timestamp":"2025-08-02T09:23:25.042Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083301129 \u001b[32m200\u001b[0m 2.944 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083306131","service":"vms-server","timestamp":"2025-08-02T09:23:25.074Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083311137","service":"vms-server","timestamp":"2025-08-02T09:23:25.077Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083306131 \u001b[32m200\u001b[0m 12.434 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083311137 \u001b[32m200\u001b[0m 15.316 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083316128","service":"vms-server","timestamp":"2025-08-02T09:23:25.095Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083321135","service":"vms-server","timestamp":"2025-08-02T09:23:25.100Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083316128 \u001b[32m200\u001b[0m 11.454 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083321135 \u001b[32m200\u001b[0m 12.396 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083326136","service":"vms-server","timestamp":"2025-08-02T09:23:25.117Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083326136 \u001b[32m200\u001b[0m 9.581 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083331136","service":"vms-server","timestamp":"2025-08-02T09:23:25.126Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083331136 \u001b[32m200\u001b[0m 9.883 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083336136","service":"vms-server","timestamp":"2025-08-02T09:23:25.132Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083336136 \u001b[32m200\u001b[0m 9.471 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083341136","service":"vms-server","timestamp":"2025-08-02T09:23:25.140Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083341136 \u001b[32m200\u001b[0m 8.902 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083346136","service":"vms-server","timestamp":"2025-08-02T09:23:25.148Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083346136 \u001b[32m200\u001b[0m 4.228 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083351136","service":"vms-server","timestamp":"2025-08-02T09:23:25.153Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083351136 \u001b[32m200\u001b[0m 2.562 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083356136","service":"vms-server","timestamp":"2025-08-02T09:23:25.162Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083356136 \u001b[32m200\u001b[0m 4.895 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083361136","service":"vms-server","timestamp":"2025-08-02T09:23:25.168Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083361136 \u001b[32m200\u001b[0m 3.124 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083366129","service":"vms-server","timestamp":"2025-08-02T09:23:25.174Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083366129 \u001b[32m200\u001b[0m 2.869 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083371132","service":"vms-server","timestamp":"2025-08-02T09:23:25.179Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083371132 \u001b[32m200\u001b[0m 2.298 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083376133","service":"vms-server","timestamp":"2025-08-02T09:23:25.184Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083376133 \u001b[32m200\u001b[0m 2.256 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"🔌 WebSocket connection established: LAN User (lan-user) from GUEST [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"🚀 Setting up authenticated connection for LAN User (lan-user) from GUEST","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"User LAN User (lan-user) joined room: department:GUEST","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"User LAN User (lan-user) joined personal room: user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"Updated existing session with socket info for LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"Total connected clients: 1, 1 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"level":"info","message":"Socket cIpJCSr8QbE87XdJAAAn is in rooms: cIpJCSr8QbE87XdJAAAn, department:GUEST, user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:23:25"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083381129","service":"vms-server","timestamp":"2025-08-02T09:23:29.842Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083381129 \u001b[32m200\u001b[0m 3.343 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:29"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083386129","service":"vms-server","timestamp":"2025-08-02T09:23:34.851Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083386129 \u001b[32m200\u001b[0m 2.761 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:34"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083391131","service":"vms-server","timestamp":"2025-08-02T09:23:39.844Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083391131 \u001b[32m200\u001b[0m 3.217 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083396131","service":"vms-server","timestamp":"2025-08-02T09:23:44.842Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083396131 \u001b[32m200\u001b[0m 5.502 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:44"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083401139","service":"vms-server","timestamp":"2025-08-02T09:23:49.856Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083401139 \u001b[32m200\u001b[0m 6.545 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083406124","service":"vms-server","timestamp":"2025-08-02T09:23:54.839Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083406124 \u001b[32m200\u001b[0m 4.127 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:54"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-08-02 09:23:54"}
{"level":"info","message":"✅ No duplicate sessions found","service":"vms-server","timestamp":"2025-08-02 09:23:54"}
{"level":"info","message":"✅ All users have unique sessions (2 unique active users)","service":"vms-server","timestamp":"2025-08-02 09:23:54"}
{"level":"info","message":"✅ Initial session cleanup completed","service":"vms-server","timestamp":"2025-08-02 09:23:54"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083411124","service":"vms-server","timestamp":"2025-08-02T09:23:59.836Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:23:59"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083411124 \u001b[32m200\u001b[0m 6.545 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:23:59"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083416138","service":"vms-server","timestamp":"2025-08-02T09:24:04.849Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:24:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083416138 \u001b[32m200\u001b[0m 2.862 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083421126","service":"vms-server","timestamp":"2025-08-02T09:24:09.838Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:24:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083421126 \u001b[32m200\u001b[0m 3.498 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:09"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 3.232 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"\u001b[0mGET /assets/index-ZPtY7c_T-1754126416197.js \u001b[32m200\u001b[0m 1.872 ms - 1182729\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"\u001b[0mGET /assets/vendor-C6oTH5a1-1754126416197.js \u001b[32m200\u001b[0m 1.284 ms - 140297\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"\u001b[0mGET /assets/ui-BXuAQCl8-1754126416197.js \u001b[32m200\u001b[0m 1.544 ms - 80749\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"\u001b[0mGET /assets/index-CEi6v1sF-1754126416197.css \u001b[32m200\u001b[0m 1.432 ms - 95768\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"\u001b[0mGET /assets/utils-khaVmnFk-1754126416197.js \u001b[32m200\u001b[0m 1.576 ms - 20714\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:24:14.358Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 2.602 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.904 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126654497","service":"vms-server","timestamp":"2025-08-02T09:24:14.501Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126654497 \u001b[32m200\u001b[0m 4.145 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-02T09:24:14.551Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"🔌 WebSocket connection established: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"🚀 Setting up authenticated connection for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined personal room: user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"Updated existing session with socket info for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276)","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"Total connected clients: 2, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"Socket 19X8BrVl_rw-z59aAAAq is in rooms: 19X8BrVl_rw-z59aAAAq, department:AUDIT, admin-users, user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126654661","service":"vms-server","timestamp":"2025-08-02T09:24:14.663Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126654661 \u001b[32m200\u001b[0m 4.238 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083426129","service":"vms-server","timestamp":"2025-08-02T09:24:14.864Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083426129 \u001b[32m200\u001b[0m 3.959 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:14"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 1.422 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:19"}
{"level":"info","message":"User disconnected: LAN User (lan-user) from department GUEST","service":"vms-server","timestamp":"2025-08-02 09:24:19"}
{"level":"info","message":"User LAN User (lan-user) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:24:19"}
{"level":"info","message":"Cleared socket info for LAN User (lan-user) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:24:19"}
{"level":"info","message":"Broadcasting 0 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:24:19"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for LAN User (GUEST) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:24:19"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 0 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:24:19"}
{"level":"info","message":"\u001b[0mGET /assets/ui-BXuAQCl8-1754126416197.js \u001b[32m200\u001b[0m 2.737 ms - 80749\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:19"}
{"level":"info","message":"\u001b[0mGET /assets/index-CEi6v1sF-1754126416197.css \u001b[32m200\u001b[0m 3.932 ms - 95768\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:19"}
{"level":"info","message":"\u001b[0mGET /assets/utils-khaVmnFk-1754126416197.js \u001b[32m200\u001b[0m 2.156 ms - 20714\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:19"}
{"level":"info","message":"\u001b[0mGET /assets/vendor-C6oTH5a1-1754126416197.js \u001b[32m200\u001b[0m 3.837 ms - 140297\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:19"}
{"level":"info","message":"\u001b[0mGET /assets/index-ZPtY7c_T-1754126416197.js \u001b[32m200\u001b[0m 2.963 ms - 1182729\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"ip":"**************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:24:20.577Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 2.926 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083431963","service":"vms-server","timestamp":"2025-08-02T09:24:20.702Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083431963 \u001b[32m200\u001b[0m 5.823 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"ip":"**************","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-02T09:24:20.750Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"🔌 WebSocket connection established: LAN User (lan-user) from GUEST [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"🚀 Setting up authenticated connection for LAN User (lan-user) from GUEST","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"User LAN User (lan-user) joined room: department:GUEST","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"User LAN User (lan-user) joined personal room: user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"Updated existing session with socket info for LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"Total connected clients: 2, 1 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"Socket a8yZM8E2UCo8IBh0AAAt is in rooms: a8yZM8E2UCo8IBh0AAAt, department:GUEST, user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083432057","service":"vms-server","timestamp":"2025-08-02T09:24:20.766Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083432057 \u001b[32m200\u001b[0m 3.315 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:20"}
{"level":"info","message":"🔍 Checking for year rollover...","service":"vms-server","timestamp":"2025-08-02 09:24:24"}
{"level":"info","message":"🗓️ Year rollover monitoring started - checking daily","service":"vms-server","timestamp":"2025-08-02 09:24:24"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:24:24"}
{"level":"info","message":"📅 Current date: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:24:24"}
{"level":"info","message":"📊 Calendar year: 2025","service":"vms-server","timestamp":"2025-08-02 09:24:24"}
{"level":"info","message":"📈 Calculated fiscal year: 2025","service":"vms-server","timestamp":"2025-08-02 09:24:24"}
{"level":"info","message":"⚙️ Configured fiscal year: 2025","service":"vms-server","timestamp":"2025-08-02 09:24:24"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-02T09:24:30.430Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: EMMANUEL AMOAKOH (AUDIT) - Role: USER","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Audit logged: EMMANUEL AMOAKOH logged in to AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Created new session for user EMMANUEL AMOAKOH (session: 5ab9ee5e-7cc7-48dd-afa2-8275e4ab66a0)","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 65.852 ms - 266\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.496 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754083441792","service":"vms-server","timestamp":"2025-08-02T09:24:30.528Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754083441792","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:24:30.547Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Found 5 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:24:30","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754083441792 \u001b[32m200\u001b[0m 38.643 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"User disconnected: LAN User (lan-user) from department GUEST","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"User LAN User (lan-user) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Cleared socket info for LAN User (lan-user) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Broadcasting 0 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for LAN User (GUEST) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 0 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 4.340 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"ip":"**************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-02T09:24:30.737Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 25.279 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"🔌 WebSocket connection established: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"🚀 Setting up authenticated connection for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined personal room: user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"ip":"**************","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-02T09:24:30.776Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Updated existing session with socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98)","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Total connected clients: 2, 2 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Socket MuoBiJ07yiB-cGEWAAAw is in rooms: MuoBiJ07yiB-cGEWAAAw, department:AUDIT, admin-users, user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 19.334 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:24:30.839Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:30.842Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.867 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"ip":"**************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:24:30.847Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:30.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.755 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 23.790 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 42.154 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:30"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:35.848Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.141 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:35"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:35.851Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.383 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:35"}
{"level":"info","message":"User disconnected: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"Cleared socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for EMMANUEL AMOAKOH (AUDIT) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:37.832Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 4.354 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"\u001b[0mGET /audit-dashboard \u001b[32m200\u001b[0m 2.690 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:37.866Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.932 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:37.871Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.785 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:37.877Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.804 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"\u001b[0mGET /assets/ui-BXuAQCl8-1754126416197.js \u001b[32m200\u001b[0m 2.834 ms - 80749\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"\u001b[0mGET /assets/utils-khaVmnFk-1754126416197.js \u001b[32m200\u001b[0m 1.117 ms - 20714\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"\u001b[0mGET /assets/index-CEi6v1sF-1754126416197.css \u001b[32m200\u001b[0m 1.362 ms - 95768\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:37"}
{"level":"info","message":"\u001b[0mGET /assets/vendor-C6oTH5a1-1754126416197.js \u001b[32m200\u001b[0m 2.397 ms - 140297\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mGET /assets/index-ZPtY7c_T-1754126416197.js \u001b[32m200\u001b[0m 2.457 ms - 1182729\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.852 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:24:38.465Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.855 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mGET /favicon.ico \u001b[32m200\u001b[0m 3.859 ms - 1150\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"🔌 WebSocket connection established: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"🚀 Setting up authenticated connection for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined personal room: user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Updated existing session with socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98)","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Total connected clients: 2, 2 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Socket 07G8_h0abaFwJBxgAAA0 is in rooms: 07G8_h0abaFwJBxgAAA0, department:AUDIT, admin-users, user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/me","service":"vms-server","timestamp":"2025-08-02T09:24:38.508Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /me","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mGET /api/auth/me \u001b[32m200\u001b[0m 7.321 ms - 202\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.732 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"✅ REAL-TIME: Confirmed department join for EMMANUEL AMOAKOH in AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Connected clients after join: SELORM (AUDIT), EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754083449825","service":"vms-server","timestamp":"2025-08-02T09:24:38.539Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754083449825","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: GET /users?_t=1754083449826","service":"vms-server","timestamp":"2025-08-02T09:24:38.541Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754083449826","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:24:38.550Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754083449826 \u001b[32m200\u001b[0m 16.452 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Found 5 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:24:38","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754083449825 \u001b[32m200\u001b[0m 22.328 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: GET /users?_t=1754083449861","service":"vms-server","timestamp":"2025-08-02T09:24:38.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754083449861","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754083449861 \u001b[32m200\u001b[0m 8.256 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754083449938","service":"vms-server","timestamp":"2025-08-02T09:24:38.679Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754083449938","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:24:38.682Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:24:38.686Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Found 5 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:24:38","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754083449938 \u001b[32m200\u001b[0m 15.198 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 30.633 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-02T09:24:38.791Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 7.750 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-02T09:24:38.810Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 6.999 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:24:38.880Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:38.882Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.296 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:24:38.885Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 13.588 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:38.896Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.874 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 24.629 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:38"}
{"level":"info","message":"User disconnected: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"Cleared socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for EMMANUEL AMOAKOH (AUDIT) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:39.789Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.836 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"\u001b[0mGET /audit-dashboard \u001b[32m200\u001b[0m 2.967 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:39.819Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 4.269 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:39.827Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 4.174 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:39.836Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 4.296 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"\u001b[0mGET /assets/ui-BXuAQCl8-1754126416197.js \u001b[32m200\u001b[0m 3.996 ms - 80749\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"\u001b[0mGET /assets/vendor-C6oTH5a1-1754126416197.js \u001b[32m200\u001b[0m 2.559 ms - 140297\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"\u001b[0mGET /assets/utils-khaVmnFk-1754126416197.js \u001b[32m200\u001b[0m 2.655 ms - 20714\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"\u001b[0mGET /assets/index-CEi6v1sF-1754126416197.css \u001b[32m200\u001b[0m 3.663 ms - 95768\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:39"}
{"level":"info","message":"\u001b[0mGET /assets/index-ZPtY7c_T-1754126416197.js \u001b[32m200\u001b[0m 1.937 ms - 1182729\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:24:40.337Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 2.101 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.250 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"\u001b[0mGET /favicon.ico \u001b[32m200\u001b[0m 1.047 ms - 1150\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"🔌 WebSocket connection established: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"🚀 Setting up authenticated connection for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined personal room: user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Updated existing session with socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98)","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Total connected clients: 2, 2 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Socket RtJsz-QgNd3fyxlCAAA5 is in rooms: RtJsz-QgNd3fyxlCAAA5, department:AUDIT, admin-users, user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/me","service":"vms-server","timestamp":"2025-08-02T09:24:40.398Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /me","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"\u001b[0mGET /api/auth/me \u001b[36m304\u001b[0m 6.926 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.350 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"✅ REAL-TIME: Confirmed department join for EMMANUEL AMOAKOH in AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Connected clients after join: SELORM (AUDIT), EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754083451716","service":"vms-server","timestamp":"2025-08-02T09:24:40.431Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754083451716","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: GET /users?_t=1754083451716","service":"vms-server","timestamp":"2025-08-02T09:24:40.433Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754083451716","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:24:40.437Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Found 5 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:24:40","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754083451716 \u001b[32m200\u001b[0m 13.455 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754083451716 \u001b[32m200\u001b[0m 14.708 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: GET /users?_t=1754083451755","service":"vms-server","timestamp":"2025-08-02T09:24:40.464Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754083451755","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754083451755 \u001b[32m200\u001b[0m 7.539 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:24:40.484Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 18.211 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-02T09:24:40.519Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 9.397 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754083451820","service":"vms-server","timestamp":"2025-08-02T09:24:40.555Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754083451820","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:24:40.560Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-02T09:24:40.564Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Found 5 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:24:40","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754083451820 \u001b[32m200\u001b[0m 16.015 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 13.462 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:24:40.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:40.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.880 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:24:40.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 9.244 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:40.627Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.746 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 14.429 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:40"}
{"level":"info","message":"User disconnected: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"Cleared socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for EMMANUEL AMOAKOH (AUDIT) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:41.508Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.375 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"\u001b[0mGET /audit-dashboard \u001b[32m200\u001b[0m 1.616 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:41.534Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.036 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:41.537Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 1.830 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:41.541Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.038 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"\u001b[0mGET /assets/ui-BXuAQCl8-1754126416197.js \u001b[32m200\u001b[0m 1.664 ms - 80749\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"\u001b[0mGET /assets/utils-khaVmnFk-1754126416197.js \u001b[32m200\u001b[0m 1.619 ms - 20714\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"\u001b[0mGET /assets/index-CEi6v1sF-1754126416197.css \u001b[32m200\u001b[0m 1.757 ms - 95768\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"\u001b[0mGET /assets/vendor-C6oTH5a1-1754126416197.js \u001b[32m200\u001b[0m 1.429 ms - 140297\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"level":"info","message":"\u001b[0mGET /assets/index-ZPtY7c_T-1754126416197.js \u001b[32m200\u001b[0m 1.148 ms - 1182729\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:41"}
{"ip":"**************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:24:42.051Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 2.602 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.542 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"\u001b[0mGET /favicon.ico \u001b[32m200\u001b[0m 1.837 ms - 1150\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"🔌 WebSocket connection established: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"🚀 Setting up authenticated connection for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined personal room: user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Updated existing session with socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98)","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Total connected clients: 2, 2 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Socket VAIS4UxAKt3NTehoAAA- is in rooms: VAIS4UxAKt3NTehoAAA-, department:AUDIT, admin-users, user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/me","service":"vms-server","timestamp":"2025-08-02T09:24:42.136Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /me","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"\u001b[0mGET /api/auth/me \u001b[36m304\u001b[0m 7.396 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 2.682 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: GET /users?_t=1754083453455","service":"vms-server","timestamp":"2025-08-02T09:24:42.175Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754083453455","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754083453455","service":"vms-server","timestamp":"2025-08-02T09:24:42.177Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754083453455","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"✅ REAL-TIME: Confirmed department join for EMMANUEL AMOAKOH in AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Connected clients after join: SELORM (AUDIT), EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:24:42.188Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Found 5 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:24:42","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754083453455 \u001b[32m200\u001b[0m 22.220 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754083453455 \u001b[32m200\u001b[0m 25.223 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: GET /users?_t=1754083453523","service":"vms-server","timestamp":"2025-08-02T09:24:42.232Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754083453523","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754083453523 \u001b[32m200\u001b[0m 4.866 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:24:42.258Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 11.934 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754083453567","service":"vms-server","timestamp":"2025-08-02T09:24:42.276Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754083453567","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:24:42.280Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Found 5 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:24:42","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754083453567 \u001b[32m200\u001b[0m 9.588 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-02T09:24:42.287Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 5.626 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-02T09:24:42.300Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 4.610 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:24:42.342Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:42.345Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.439 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:24:42.352Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 25.765 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:42.369Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.450 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 33.658 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:42"}
{"level":"info","message":"User disconnected: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"Cleared socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for EMMANUEL AMOAKOH (AUDIT) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:45.869Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 3.354 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"\u001b[0mGET /audit-dashboard \u001b[32m200\u001b[0m 2.386 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:45.905Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 4.369 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:45.911Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.946 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:24:45.918Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.706 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"\u001b[0mGET /assets/utils-khaVmnFk-1754126416197.js \u001b[32m200\u001b[0m 5.800 ms - 20714\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"\u001b[0mGET /assets/ui-BXuAQCl8-1754126416197.js \u001b[32m200\u001b[0m 9.376 ms - 80749\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"\u001b[0mGET /assets/index-CEi6v1sF-1754126416197.css \u001b[32m200\u001b[0m 1.411 ms - 95768\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:45"}
{"level":"info","message":"\u001b[0mGET /assets/vendor-C6oTH5a1-1754126416197.js \u001b[32m200\u001b[0m 5.752 ms - 140297\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mGET /assets/index-ZPtY7c_T-1754126416197.js \u001b[32m200\u001b[0m 5.539 ms - 1182729\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:24:46.415Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.912 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.763 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mGET /favicon.ico \u001b[32m200\u001b[0m 3.641 ms - 1150\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"🔌 WebSocket connection established: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"🚀 Setting up authenticated connection for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined personal room: user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Updated existing session with socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98)","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Total connected clients: 2, 2 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Socket rSm0yrMIkSavpt10AABC is in rooms: rSm0yrMIkSavpt10AABC, department:AUDIT, admin-users, user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/me","service":"vms-server","timestamp":"2025-08-02T09:24:46.559Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /me","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mGET /api/auth/me \u001b[36m304\u001b[0m 6.022 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"✅ REAL-TIME: Confirmed department join for EMMANUEL AMOAKOH in AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Connected clients after join: SELORM (AUDIT), EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.606 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: GET /users?_t=1754083457874","service":"vms-server","timestamp":"2025-08-02T09:24:46.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754083457874","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754083457874","service":"vms-server","timestamp":"2025-08-02T09:24:46.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754083457874","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754083457874 \u001b[32m200\u001b[0m 15.185 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:24:46.634Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Found 5 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:24:46","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754083457874 \u001b[32m200\u001b[0m 27.373 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: GET /users?_t=1754083457938","service":"vms-server","timestamp":"2025-08-02T09:24:46.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754083457938","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754083457938 \u001b[32m200\u001b[0m 4.275 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:24:46.668Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 18.422 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754083457979","service":"vms-server","timestamp":"2025-08-02T09:24:46.690Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754083457979","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:24:46.696Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Found 5 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:24:46","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754083457979 \u001b[32m200\u001b[0m 14.764 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-02T09:24:46.708Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 7.841 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-02T09:24:46.739Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 9.106 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:24:46.783Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:46.786Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.371 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:24:46.794Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:46.798Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.163 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 19.186 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 29.445 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:46"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:51.792Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.319 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:51"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:51.796Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.106 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:51"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:56.803Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.460 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:56"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:24:56.807Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.529 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:56"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-02T09:24:57.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"warn","message":"Login attempt failed - wrong password: SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:24:57"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[33m401\u001b[0m 3.484 ms - 84\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:24:57"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:01.798Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.404 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:01"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:01.801Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.222 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:01"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-02T09:25:06.033Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: SELORM (AUDIT) - Role: USER","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Audit logged: SELORM logged in to AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Created new session for user SELORM (session: 1102df72-c4b0-4f69-9284-59bc11487d07)","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 36.465 ms - 256\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.600 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754126706073","service":"vms-server","timestamp":"2025-08-02T09:25:06.079Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754126706073","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:25:06.083Z by user SELORM (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Found 5 vouchers for request from SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:25:06","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754126706073 \u001b[32m200\u001b[0m 10.247 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"User disconnected: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Cleared socket info for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for SELORM (AUDIT) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 2.296 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-02T09:25:06.216Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"🔌 WebSocket connection established: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"🚀 Setting up authenticated connection for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined personal room: user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 19.367 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Updated existing session with socket info for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276)","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Total connected clients: 2, 2 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Socket XuBzbGk7XAvQbiSdAABF is in rooms: XuBzbGk7XAvQbiSdAABF, department:AUDIT, admin-users, user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-02T09:25:06.242Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 5.600 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:25:06.261Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 5.377 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:06.268Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.439 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:25:06.272Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 9.769 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:06.283Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.218 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:06.797Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.644 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:06.801Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.139 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:06"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:11.274Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.503 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:11"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:11.277Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.078 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:11"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:11.802Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.232 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:11"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:11.806Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.382 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:11"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126716115","service":"vms-server","timestamp":"2025-08-02T09:25:16.118Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:25:16"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126716115 \u001b[32m200\u001b[0m 4.033 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-02T09:25:16.129Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-02 09:25:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126716135","service":"vms-server","timestamp":"2025-08-02T09:25:16.136Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:25:16"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126716135 \u001b[32m200\u001b[0m 2.855 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:16"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:16.799Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.008 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:16"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:16.803Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.210 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:16"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:25:16.808Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:16"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:25:16"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 19.997 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:16"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:21.792Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.243 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:21"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:21.796Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.017 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:21"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:26.853Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.747 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:26"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:26.857Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.864 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:26"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:31.791Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.196 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:31"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:31.794Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.857 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:31"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:36.793Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.723 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:36"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:36.799Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.731 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:36"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 1.167 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:36"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:25:36.896Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.441 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:36"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.834 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126737000","service":"vms-server","timestamp":"2025-08-02T09:25:37.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126737000 \u001b[32m200\u001b[0m 4.414 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-02T09:25:37.073Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"🔌 WebSocket connection established: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"🚀 Setting up authenticated connection for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"Existing connection: XuBzbGk7XAvQbiSdAABF with last activity at 2025-08-02T09:25:36.250Z","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined personal room: user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"Updated existing session with socket info for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276)","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"Total connected clients: 3, 3 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"Socket 4v30QYmbMBEcv0nrAABI is in rooms: 4v30QYmbMBEcv0nrAABI, department:AUDIT, admin-users, user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126737163","service":"vms-server","timestamp":"2025-08-02T09:25:37.165Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126737163 \u001b[32m200\u001b[0m 2.633 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:37"}
{"level":"info","message":"User disconnected: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:39"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) still has 1 active connections","service":"vms-server","timestamp":"2025-08-02 09:25:39"}
{"level":"info","message":"Cleared socket info for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:25:39"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:39"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for SELORM (AUDIT) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:25:39"}
{"level":"info","message":"After disconnect - Total connected clients: 2, 2 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:39"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:25:39.142Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:25:39"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.397 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:39"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 2.084 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:39"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:25:39.165Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:25:39"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 3.101 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:39"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:25:39.173Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:25:39"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.159 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:39"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:25:39.183Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:25:39"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 3.654 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:39"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:41.789Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.849 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:41"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:41.792Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.091 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:41"}
{"level":"info","message":"User disconnected: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:25:44.475Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.470 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"Cleared socket info for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for SELORM (AUDIT) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 5.255 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 1.915 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:25:44.495Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.678 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:25:44.499Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.549 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:25:44.503Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.958 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:25:44.521Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 2.270 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.556 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126744534","service":"vms-server","timestamp":"2025-08-02T09:25:44.539Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126744534 \u001b[32m200\u001b[0m 6.110 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-02T09:25:44.564Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"🔌 WebSocket connection established: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"🚀 Setting up authenticated connection for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined personal room: user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126744569","service":"vms-server","timestamp":"2025-08-02T09:25:44.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Updated existing session with socket info for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276)","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"Total connected clients: 2, 2 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"Socket 79_vnRdStiPgfUcdAABO is in rooms: 79_vnRdStiPgfUcdAABO, department:AUDIT, admin-users, user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126744569 \u001b[32m200\u001b[0m 9.620 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:44"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:46.801Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.039 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:46"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:46.805Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.505 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:46"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:25:46.811Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:46"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:25:46"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 20.398 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:46"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:51.793Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.573 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:51"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:51.797Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.012 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:51"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-02T09:25:54.240Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: SELORM (AUDIT) - Role: USER","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Audit logged: SELORM logged in to AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Created new session for user SELORM (session: 791d074a-6886-4130-99a6-3cf7424ebe40)","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 50.673 ms - 256\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.578 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754126754295","service":"vms-server","timestamp":"2025-08-02T09:25:54.300Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754126754295","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:25:54.305Z by user SELORM (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Found 5 vouchers for request from SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:25:54","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754126754295 \u001b[32m200\u001b[0m 11.619 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"User disconnected: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Cleared socket info for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for SELORM (AUDIT) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-02T09:25:54.445Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 2.918 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 7.545 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"🔌 WebSocket connection established: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"🚀 Setting up authenticated connection for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined personal room: user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-02T09:25:54.465Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Updated existing session with socket info for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276)","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Total connected clients: 2, 2 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Socket Yl4b36secE8dvdjOAABR is in rooms: Yl4b36secE8dvdjOAABR, department:AUDIT, admin-users, user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 11.427 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:25:54.491Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 4.952 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:54.498Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.302 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:25:54.500Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 10.502 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:54.759Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.783 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:54"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:56.789Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.457 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:56"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:56.792Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.285 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:56"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:25:58.240Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:25:58"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.712 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:58"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:59.782Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.084 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:59"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:25:59.786Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.017 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:25:59"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:26:01.771Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:26:01"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 3.737 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:01"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:01.800Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.082 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:01"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:01.803Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.395 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:01"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:04.776Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.817 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:04"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:04.778Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.234 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:04"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:06.795Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.779 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:06"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:06.798Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.613 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:06"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:09.771Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.065 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:09"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:09.772Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.423 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:09"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:11.790Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.403 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:11"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:11.793Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.222 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:11"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:14.772Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.870 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:14"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:14.774Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.604 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:14"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:16.805Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.520 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:16"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:16.807Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.687 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:16"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:26:16.812Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:26:16"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:26:16"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 17.924 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:19.778Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.203 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:19"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:19.780Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.676 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:19"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:21.955Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.727 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:21"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:21.959Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.392 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:21"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:24.775Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.457 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:24.779Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.867 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:26:24.782Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:26:24"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:26:24"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 19.599 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:24"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:26.791Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.389 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:26"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:26.794Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.661 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:26"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:29.774Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.300 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:29"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:29.778Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.885 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:29"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:31.788Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.790 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:31"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:31.791Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.639 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:31"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:34.765Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.609 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:34.767Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.189 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:34"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:36.794Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.291 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:36"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:36.796Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.254 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:36"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:39.772Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.573 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:39"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:39.774Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.556 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:39"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:41.792Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.227 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:41"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:41.794Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.433 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:41"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:44.774Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.361 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:44.776Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.833 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:44"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.757 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:46"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:46.787Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.216 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:46"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:46.790Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.662 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:46"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:26:46.804Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:26:46"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:26:46"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 174.898 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:46"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:49.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.974 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:49"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:49.772Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.337 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:49"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:51.792Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.346 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:51"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:51.794Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.434 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:51"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:54.774Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.505 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:54"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:54.776Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.538 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:54"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:26:54.777Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:26:54"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:26:54"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 15.973 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:54"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:56.783Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.802 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:56"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:26:56.787Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.523 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:56"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:26:58.306Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 1.781 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"User disconnected: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Cleared socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for EMMANUEL AMOAKOH (AUDIT) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"\u001b[0mGET /?logout=true \u001b[0m-\u001b[0m - ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/logout","service":"vms-server","timestamp":"2025-08-02T09:26:58.341Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /logout","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 3.475 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"ip":"**************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-02T09:26:58.349Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 13.864 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"🔌 WebSocket connection established: LAN User (lan-user) from GUEST [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"🚀 Setting up authenticated connection for LAN User (lan-user) from GUEST","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"User LAN User (lan-user) joined room: department:GUEST","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"User LAN User (lan-user) joined personal room: user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Audit logged: EMMANUEL AMOAKOH logged out from AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) logged out successfully. Session ID: 5ab9ee5e-7cc7-48dd-afa2-8275e4ab66a0","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"\u001b[0mPOST /api/auth/logout \u001b[32m200\u001b[0m 33.567 ms - 37\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Updated existing session with socket info for LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Total connected clients: 2, 1 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Socket g-38UPvGH-Ci4MDHAABU is in rooms: g-38UPvGH-Ci4MDHAABU, department:GUEST, user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"ip":"**************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:26:58.398Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.498 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083589731","service":"vms-server","timestamp":"2025-08-02T09:26:58.434Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083589731 \u001b[32m200\u001b[0m 7.091 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"ip":"**************","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-02T09:26:58.524Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"🔌 WebSocket connection established: LAN User (lan-user) from GUEST [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"🚀 Setting up authenticated connection for LAN User (lan-user) from GUEST","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"User LAN User (lan-user) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Existing connection: g-38UPvGH-Ci4MDHAABU with last activity at 2025-08-02T09:26:58.369Z","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"User LAN User (lan-user) joined room: department:GUEST","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"User LAN User (lan-user) joined personal room: user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Updated existing session with socket info for LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Total connected clients: 3, 2 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"Socket 6Qsl2g7HPKJ6dLmNAABX is in rooms: 6Qsl2g7HPKJ6dLmNAABX, department:GUEST, user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083589840","service":"vms-server","timestamp":"2025-08-02T09:26:58.548Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083589840 \u001b[32m200\u001b[0m 6.685 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:26:58"}
{"level":"warn","message":"🔍 Stale connection detected: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) - 4s since last heartbeat","service":"vms-server","timestamp":"2025-08-02 09:27:00"}
{"level":"info","message":"🧹 Cleaning up stale connection: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276)","service":"vms-server","timestamp":"2025-08-02 09:27:00"}
{"level":"info","message":"User disconnected: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:27:00"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:27:00"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for stale connection: SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:27:00"}
{"level":"info","message":"Broadcasting 0 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:27:00"}
{"level":"info","message":"🧹 Cleaned up 1 stale connections","service":"vms-server","timestamp":"2025-08-02 09:27:00"}
{"level":"info","message":"Cleared socket info for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:27:00"}
{"level":"info","message":"Broadcasting 0 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:27:00"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for SELORM (AUDIT) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:27:00"}
{"level":"info","message":"After disconnect - Total connected clients: 2, 0 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:27:00"}
{"level":"warn","message":"🔍 Stale connection detected: LAN User (lan-user) - 4s since last heartbeat","service":"vms-server","timestamp":"2025-08-02 09:27:02"}
{"level":"info","message":"🧹 Cleaning up stale connection: LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:27:02"}
{"level":"info","message":"User disconnected: LAN User (lan-user) from department GUEST","service":"vms-server","timestamp":"2025-08-02 09:27:02"}
{"level":"info","message":"User LAN User (lan-user) still has 1 active connections","service":"vms-server","timestamp":"2025-08-02 09:27:02"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for stale connection: LAN User (GUEST)","service":"vms-server","timestamp":"2025-08-02 09:27:02"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:27:02"}
{"level":"info","message":"🧹 Cleaned up 1 stale connections","service":"vms-server","timestamp":"2025-08-02 09:27:02"}
{"level":"info","message":"Cleared socket info for LAN User (lan-user) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:27:02"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:27:02"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for LAN User (GUEST) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:27:02"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:27:02"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:27:06.201Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.521 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:27:06"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:27:06.203Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.368 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:27:06"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:27:07.495Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","message":"❌ Immediate logout error: Invalid time value","service":"vms-server","stack":"RangeError: Invalid time value\n    at Date.toISOString (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\routes\\auth.js:236:107\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 2.000 ms - 46\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"\u001b[0mGET /?logout=true \u001b[32m200\u001b[0m 1.237 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/logout","service":"vms-server","timestamp":"2025-08-02T09:27:07.517Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /logout","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-02T09:27:07.529Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 4.944 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[0m-\u001b[0m - ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"\u001b[0mPOST /api/auth/logout \u001b[0m-\u001b[0m - ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"Audit logged: SELORM logged out from AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) logged out successfully. Session ID: 791d074a-6886-4130-99a6-3cf7424ebe40","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:27:07.839Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.957 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126827580","service":"vms-server","timestamp":"2025-08-02T09:27:07.842Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126827580 \u001b[32m200\u001b[0m 5.927 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-02T09:27:07.848Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"🔌 WebSocket connection established: LAN User (lan-user) from GUEST [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"🚀 Setting up authenticated connection for LAN User (lan-user) from GUEST","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"User LAN User (lan-user) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"Existing connection: 6Qsl2g7HPKJ6dLmNAABX with last activity at 2025-08-02T09:27:06.553Z","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"User LAN User (lan-user) joined room: department:GUEST","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"User LAN User (lan-user) joined personal room: user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"Updated existing session with socket info for LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"Total connected clients: 2, 2 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"Socket yfkpVZ1xsjrsxxN_AABd is in rooms: yfkpVZ1xsjrsxxN_AABd, department:GUEST, user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126827899","service":"vms-server","timestamp":"2025-08-02T09:27:07.922Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126827899 \u001b[32m200\u001b[0m 11.097 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:27:07"}
{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-08-02 09:28:01"}
{"level":"info","message":"🎯 Using forced port: 8080","service":"vms-server","timestamp":"2025-08-02 09:28:01"}
{"level":"info","message":"🔧 Setting up Socket.IO middleware and handlers...","service":"vms-server","timestamp":"2025-08-02 09:28:01"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-08-02 09:28:01"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-08-02 09:28:01"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-08-02 09:28:01"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-08-02 09:28:01"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-08-02 09:28:01"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-08-02 09:28:01"}
{"level":"info","message":"Starting workflow state migration...","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"No vouchers need workflow state migration","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Users already exist (10 users found)","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🔄 Initializing workflow service...","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Initializing workflow service...","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🔄 Initializing file storage...","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"File storage initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"✅ File storage initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🌐 Environment: DEVELOPMENT","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"📊 Process ID: 1108","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"New database connection established: 687","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Database connection test successful","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Database 'vms_production' verified","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Essential database tables verified","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Database setup verification complete","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Health checks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Database Manager initialized successfully","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"✅ Session cleanup scheduled (every 10 minutes)","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🛑 Automated backup scheduler stopped","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🕒 Starting automated backup scheduler (every 24 hours)","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"✅ Automated backup scheduler started (daily backups)","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"   🖥️  VMS System: http://************:8080","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"   🖥️  VMS System: http://10.240.224.232:8080","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"   🔌 WebSocket: http://10.240.224.232:8080/socket.io/","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"   💾 Memory: 64MB RSS","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"   🔄 Uptime: 1s","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1754126882242,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🔌 WebSocket connection established: LAN User (lan-user) from GUEST [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🚀 Setting up authenticated connection for LAN User (lan-user) from GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"User LAN User (lan-user) joined room: department:GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"User LAN User (lan-user) joined personal room: user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Updated existing session with socket info for LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Total connected clients: 1, 1 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Socket 4kCMpQFCkcranq5lAAAB is in rooms: 4kCMpQFCkcranq5lAAAB, department:GUEST, user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083631070","service":"vms-server","timestamp":"2025-08-02T09:28:02.583Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083631070 \u001b[32m200\u001b[0m 7.831 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083636070","service":"vms-server","timestamp":"2025-08-02T09:28:02.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083636070 \u001b[32m200\u001b[0m 3.200 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083641068","service":"vms-server","timestamp":"2025-08-02T09:28:02.671Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083641068 \u001b[32m200\u001b[0m 2.624 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083646072","service":"vms-server","timestamp":"2025-08-02T09:28:02.720Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083646072 \u001b[32m200\u001b[0m 21.358 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083651066","service":"vms-server","timestamp":"2025-08-02T09:28:02.761Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083651066 \u001b[32m200\u001b[0m 3.007 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🔌 WebSocket connection established: LAN User (lan-user) from GUEST [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"🚀 Setting up authenticated connection for LAN User (lan-user) from GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"User LAN User (lan-user) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Existing connection: 4kCMpQFCkcranq5lAAAB with last activity at 2025-08-02T09:28:02.440Z","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"User LAN User (lan-user) joined room: department:GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"User LAN User (lan-user) joined personal room: user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Updated existing session with socket info for LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Total connected clients: 2, 2 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"level":"info","message":"Socket uHsb4dYnNwr19a9DAAAM is in rooms: uHsb4dYnNwr19a9DAAAM, department:GUEST, user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:28:02"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083656070","service":"vms-server","timestamp":"2025-08-02T09:28:04.772Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083656070 \u001b[32m200\u001b[0m 7.612 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:04"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126884769","service":"vms-server","timestamp":"2025-08-02T09:28:04.781Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:04"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126884769 \u001b[32m200\u001b[0m 9.185 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:04"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083661060","service":"vms-server","timestamp":"2025-08-02T09:28:09.766Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:09"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083661060 \u001b[32m200\u001b[0m 6.223 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:09"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083666059","service":"vms-server","timestamp":"2025-08-02T09:28:14.763Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:14"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083666059 \u001b[32m200\u001b[0m 6.891 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:14"}
{"level":"warn","message":"🔍 Stale connection detected: LAN User (lan-user) - 4s since last heartbeat","service":"vms-server","timestamp":"2025-08-02 09:28:18"}
{"level":"info","message":"🧹 Cleaning up stale connection: LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:28:18"}
{"level":"info","message":"User disconnected: LAN User (lan-user) from department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:18"}
{"level":"info","message":"User LAN User (lan-user) still has 1 active connections","service":"vms-server","timestamp":"2025-08-02 09:28:18"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for stale connection: LAN User (GUEST)","service":"vms-server","timestamp":"2025-08-02 09:28:18"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:18"}
{"level":"info","message":"🧹 Cleaned up 1 stale connections","service":"vms-server","timestamp":"2025-08-02 09:28:18"}
{"level":"info","message":"Cleared socket info for LAN User (lan-user) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:28:18"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:18"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for LAN User (GUEST) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:28:18"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:18"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083671061","service":"vms-server","timestamp":"2025-08-02T09:28:19.766Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:19"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083671061 \u001b[32m200\u001b[0m 4.631 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:19"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083676061","service":"vms-server","timestamp":"2025-08-02T09:28:24.766Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083676061 \u001b[32m200\u001b[0m 6.618 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:24"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083681065","service":"vms-server","timestamp":"2025-08-02T09:28:29.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:29"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083681065 \u001b[32m200\u001b[0m 3.487 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:29"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-08-02 09:28:32"}
{"level":"info","message":"🔍 Found 1 users with duplicate sessions","service":"vms-server","timestamp":"2025-08-02 09:28:32"}
{"level":"info","message":"🧹 Cleaned 1 duplicate sessions for user SELORM","service":"vms-server","timestamp":"2025-08-02 09:28:32"}
{"level":"info","message":"✅ Session deduplication complete: cleaned 1 duplicate sessions","service":"vms-server","timestamp":"2025-08-02 09:28:32"}
{"level":"info","message":"✅ All users have unique sessions (2 unique active users)","service":"vms-server","timestamp":"2025-08-02 09:28:32"}
{"level":"info","message":"✅ Initial session cleanup completed","service":"vms-server","timestamp":"2025-08-02 09:28:32"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083686067","service":"vms-server","timestamp":"2025-08-02T09:28:34.771Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:34"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083686067 \u001b[32m200\u001b[0m 2.893 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126917775","service":"vms-server","timestamp":"2025-08-02T09:28:38.093Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:38"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126917775 \u001b[32m200\u001b[0m 3.197 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:38"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083691072","service":"vms-server","timestamp":"2025-08-02T09:28:39.776Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:39"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083691072 \u001b[32m200\u001b[0m 2.652 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:39"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083696073","service":"vms-server","timestamp":"2025-08-02T09:28:44.776Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083696073 \u001b[32m200\u001b[0m 3.295 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:28:45.178Z"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User test-user-456 (browser_close_test_fixed) at 2025-08-02T09:28:45.115Z","service":"vms-server","timestamp":"2025-08-02 09:28:45"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: Session test-session-789 deactivated, locks released","service":"vms-server","timestamp":"2025-08-02 09:28:45"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 24.793 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:45"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083701060","service":"vms-server","timestamp":"2025-08-02T09:28:49.764Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:49"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083701060 \u001b[32m200\u001b[0m 5.123 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:49"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083706072","service":"vms-server","timestamp":"2025-08-02T09:28:54.777Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:54"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083706072 \u001b[32m200\u001b[0m 5.174 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:54"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 2.273 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"User disconnected: LAN User (lan-user) from department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"User LAN User (lan-user) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"Cleared socket info for LAN User (lan-user) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"Broadcasting 0 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for LAN User (GUEST) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"ip":"**************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:28:55.501Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 2.680 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083706843","service":"vms-server","timestamp":"2025-08-02T09:28:55.541Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083706843 \u001b[32m200\u001b[0m 2.586 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"ip":"**************","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-02T09:28:55.571Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"🔌 WebSocket connection established: LAN User (lan-user) from GUEST [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"🚀 Setting up authenticated connection for LAN User (lan-user) from GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"User LAN User (lan-user) joined room: department:GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"User LAN User (lan-user) joined personal room: user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"Updated existing session with socket info for LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"Total connected clients: 1, 1 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"Socket UG7pSKF5GYhk-dd6AAAP is in rooms: UG7pSKF5GYhk-dd6AAAP, department:GUEST, user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"ip":"**************","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754083706891","service":"vms-server","timestamp":"2025-08-02T09:28:55.594Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754083706891 \u001b[32m200\u001b[0m 2.388 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:28:55"}
{"level":"info","message":"🔍 Checking for year rollover...","service":"vms-server","timestamp":"2025-08-02 09:29:01"}
{"level":"info","message":"🗓️ Year rollover monitoring started - checking daily","service":"vms-server","timestamp":"2025-08-02 09:29:01"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:29:01"}
{"level":"info","message":"📅 Current date: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:29:01"}
{"level":"info","message":"📊 Calendar year: 2025","service":"vms-server","timestamp":"2025-08-02 09:29:01"}
{"level":"info","message":"📈 Calculated fiscal year: 2025","service":"vms-server","timestamp":"2025-08-02 09:29:01"}
{"level":"info","message":"⚙️ Configured fiscal year: 2025","service":"vms-server","timestamp":"2025-08-02 09:29:01"}
{"ip":"**************","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-02T09:29:20.166Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: SELORM (AUDIT) - Role: USER","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Audit logged: SELORM logged in to AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Created new session for user SELORM (session: a3b871a9-6c06-460c-a4e7-ad814d523dc4)","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 53.765 ms - 256\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.129 ms - 288\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754083731534","service":"vms-server","timestamp":"2025-08-02T09:29:20.244Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754083731534","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:29:20.255Z by user SELORM (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Found 5 vouchers for request from SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:29:20","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754083731534 \u001b[32m200\u001b[0m 20.964 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"User disconnected: LAN User (lan-user) from department GUEST","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"User LAN User (lan-user) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Cleared socket info for LAN User (lan-user) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Broadcasting 0 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for LAN User (GUEST) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 1.604 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"ip":"**************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-02T09:29:20.430Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"WebSocket connection from ************** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"🔌 WebSocket connection established: SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"🚀 Setting up authenticated connection for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"User SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276) joined personal room: user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 26.469 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Updated existing session with socket info for SELORM (8aa62d84-53de-4e65-8f7f-bb8e87e92276)","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Socket FYFx-1VgGcfdYRE6AAAS is in rooms: FYFx-1VgGcfdYRE6AAAS, department:AUDIT, admin-users, user:8aa62d84-53de-4e65-8f7f-bb8e87e92276","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"ip":"**************","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-02T09:29:20.465Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 8.978 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:29:20.502Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:20.506Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.036 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"ip":"**************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:29:20.508Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 12.320 ms - 167\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:20.516Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.772 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 25.830 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:20"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=**********622","service":"vms-server","timestamp":"2025-08-02T09:29:24.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:29:24"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=**********622 \u001b[32m200\u001b[0m 5.751 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:24"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:25.514Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.556 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:25"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:25.516Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.399 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:25"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 1.821 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-02T09:29:27.076Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.263 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126967096","service":"vms-server","timestamp":"2025-08-02T09:29:27.099Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126967096 \u001b[32m200\u001b[0m 4.376 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-02T09:29:27.117Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"🔌 WebSocket connection established: LAN User (lan-user) from GUEST [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"🚀 Setting up authenticated connection for LAN User (lan-user) from GUEST","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"User LAN User (lan-user) joined room: department:GUEST","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"User LAN User (lan-user) joined personal room: user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"Broadcasting 1 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"Updated existing session with socket info for LAN User (lan-user)","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"Total connected clients: 2, 1 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"Socket PMCGK4IHSf6arXQ8AAAV is in rooms: PMCGK4IHSf6arXQ8AAAV, department:GUEST, user:lan-user","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754126967126","service":"vms-server","timestamp":"2025-08-02T09:29:27.365Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754126967126 \u001b[32m200\u001b[0m 3.620 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:27"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:30.518Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.308 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:30"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:30.522Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.489 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-02T09:29:34.407Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: EMMANUEL AMOAKOH (AUDIT) - Role: USER","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Audit logged: EMMANUEL AMOAKOH logged in to AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Created new session for user EMMANUEL AMOAKOH (session: b862b2c6-e850-4f17-86ab-ff9a92c9e191)","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 172.280 ms - 266\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.621 ms - 288\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754126974582","service":"vms-server","timestamp":"2025-08-02T09:29:34.589Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754126974582","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:29:34.594Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Found 5 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:29:34","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754126974582 \u001b[32m200\u001b[0m 14.994 ms - 8883\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"User disconnected: LAN User (lan-user) from department GUEST","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"User LAN User (lan-user) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Cleared socket info for LAN User (lan-user) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Broadcasting 0 connected users for department GUEST","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for LAN User (GUEST) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 0 in department GUEST","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 2.810 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-02T09:29:34.736Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"🔌 WebSocket connection established: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"🚀 Setting up authenticated connection for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: admin-users","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined personal room: user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Broadcasting 2 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[32m200\u001b[0m 32.435 ms - 194\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-02T09:29:34.766Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Updated existing session with socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98)","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Total connected clients: 2, 2 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Socket s3NzLEMFo2VKd_kgAAAY is in rooms: s3NzLEMFo2VKd_kgAAAY, department:AUDIT, admin-users, user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 42.803 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:29:34.822Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:34.825Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.805 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-02T09:29:34.829Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 12.196 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:34.840Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.897 ms - 184\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 28.595 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:34"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:35.521Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.648 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:35"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:35.525Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.832 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:35"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:39.825Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.106 ms - 184\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:39"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:39.828Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.369 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:39"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:40.593Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.581 ms - 184\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:40"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:40.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.784 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:40"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?timestamp=1754126982628","service":"vms-server","timestamp":"2025-08-02T09:29:42.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?timestamp=1754126982628","service":"vms-server","timestamp":"2025-08-02 09:29:42"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:29:42.642Z by user EMMANUEL AMOAKOH (AUDIT), query department: none","service":"vms-server","timestamp":"2025-08-02 09:29:42"}
{"level":"info","message":"Fetching all vouchers for AUDIT user","service":"vms-server","timestamp":"2025-08-02 09:29:42"}
{"level":"info","message":"Found 16 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:29:42"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, PSOAUG0003-PENTSOS-RETURN-COPY (c5554dcf-5831-462c-a702-b61b3a078813): VOUCHER RETURNED","service":"vms-server","timestamp":"2025-08-02 09:29:42"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:29:42","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?timestamp=1754126982628 \u001b[32m200\u001b[0m 22.401 ms - 25832\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:42"}
{"level":"info","message":"🔄 LOCK REQUEST: Re-authenticated EMMANUEL AMOAKOH (AUDIT) from current session","service":"vms-server","timestamp":"2025-08-02 09:29:44"}
{"level":"info","message":"✅ AUDIT EXCLUSIVE ACCESS: EMMANUEL AMOAKOH accessing FINANCE voucher hub","service":"vms-server","timestamp":"2025-08-02 09:29:44"}
{"level":"info","message":"🔒 ISOLATED LOCK REQUEST: EMMANUEL AMOAKOH (AUDIT) requesting voucher hub lock: AUDIT-VOUCHER-HUB-FINANCE","service":"vms-server","timestamp":"2025-08-02 09:29:44"}
{"level":"info","message":"🔒 ISOLATED ACCESS: VOUCHER HUB lock acquired - AUDIT-VOUCHER-HUB-FINANCE by EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:29:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:44.824Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.702 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:44.833Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 19.772 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:44"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:45.517Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.349 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:45"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:45.525Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 9.450 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:45"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:49.838Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.179 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:49"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:49.842Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.156 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:49"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:50.521Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.837 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:50"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:50.524Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.565 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:50"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:29:50.531Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:29:50"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:29:50"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 18.696 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:50"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:54.830Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.842 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:54"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:54.832Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.700 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:54"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:55.524Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.265 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:55"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:55.527Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.104 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:55"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?timestamp=1754083770512","service":"vms-server","timestamp":"2025-08-02T09:29:59.212Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?timestamp=1754083770512","service":"vms-server","timestamp":"2025-08-02 09:29:59"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:29:59.225Z by user SELORM (AUDIT), query department: none","service":"vms-server","timestamp":"2025-08-02 09:29:59"}
{"level":"info","message":"Fetching all vouchers for AUDIT user","service":"vms-server","timestamp":"2025-08-02 09:29:59"}
{"level":"info","message":"Found 16 vouchers for request from SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:29:59"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, PSOAUG0003-PENTSOS-RETURN-COPY (c5554dcf-5831-462c-a702-b61b3a078813): VOUCHER RETURNED","service":"vms-server","timestamp":"2025-08-02 09:29:59"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:29:59","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?timestamp=1754083770512 \u001b[32m200\u001b[0m 26.073 ms - 25832\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:59"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:59.837Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.896 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:59"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:29:59.839Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.885 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:29:59"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:00.510Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.638 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:00"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:00.513Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.811 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:00"}
{"level":"info","message":"🔄 LOCK REQUEST: Re-authenticated SELORM (AUDIT) from current session","service":"vms-server","timestamp":"2025-08-02 09:30:04"}
{"level":"info","message":"✅ AUDIT EXCLUSIVE ACCESS: SELORM accessing FINANCE voucher hub","service":"vms-server","timestamp":"2025-08-02 09:30:04"}
{"level":"info","message":"🔒 ISOLATED LOCK REQUEST: SELORM (AUDIT) requesting voucher hub lock: AUDIT-VOUCHER-HUB-FINANCE","service":"vms-server","timestamp":"2025-08-02 09:30:04"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:04.828Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.242 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:04"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:04.842Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 23.600 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:04"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:30:04.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:30:04"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:30:04"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 62.175 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:04"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:05.506Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.941 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:05"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:05.511Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.027 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:05"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:09.843Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 28.910 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:09"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:09.946Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 228.799 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:10"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:10.520Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 115.382 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:10"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:10.753Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 373.855 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:10"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:14.837Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.765 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:14"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:14.841Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.878 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:14"}
{"level":"info","message":"🔓 NAVIGATION RELEASE: EMMANUEL AMOAKOH (AUDIT) releasing lock for AUDIT-VOUCHER-HUB-FINANCE due to navigation","service":"vms-server","timestamp":"2025-08-02 09:30:15"}
{"level":"info","message":"🔓 ISOLATED ACCESS RELEASED: VOUCHER HUB lock released - AUDIT-VOUCHER-HUB-FINANCE by EMMANUEL AMOAKOH","service":"vms-server","timestamp":"2025-08-02 09:30:15"}
{"level":"info","message":"🔓 NAVIGATION RELEASE: EMMANUEL AMOAKOH (AUDIT) releasing lock for AUDIT-VOUCHER-HUB-FINANCE due to navigation","service":"vms-server","timestamp":"2025-08-02 09:30:15"}
{"level":"info","message":"🔓 NAVIGATION RELEASE: EMMANUEL AMOAKOH (AUDIT) releasing lock for AUDIT-VOUCHER-HUB-FINANCE due to navigation","service":"vms-server","timestamp":"2025-08-02 09:30:15"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:15.518Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.954 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:15"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:15.522Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.549 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:15"}
{"level":"info","message":"🔄 LOCK REQUEST: Re-authenticated SELORM (AUDIT) from current session","service":"vms-server","timestamp":"2025-08-02 09:30:18"}
{"level":"info","message":"✅ AUDIT EXCLUSIVE ACCESS: SELORM accessing FINANCE voucher hub","service":"vms-server","timestamp":"2025-08-02 09:30:18"}
{"level":"info","message":"🔒 ISOLATED LOCK REQUEST: SELORM (AUDIT) requesting voucher hub lock: AUDIT-VOUCHER-HUB-FINANCE","service":"vms-server","timestamp":"2025-08-02 09:30:18"}
{"level":"info","message":"🔒 ISOLATED ACCESS: VOUCHER HUB lock acquired - AUDIT-VOUCHER-HUB-FINANCE by SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:30:18"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:19.826Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 7.126 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:19"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:19.841Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 28.379 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:19"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:20.512Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.744 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:20"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:20.517Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.857 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:20"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:30:20.520Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:30:20"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:30:20"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 19.872 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:20"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:24.826Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.903 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:24.828Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.680 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:24"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:25.507Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.781 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:25"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:25.511Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.071 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:25"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:29.836Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.357 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:29"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:29.839Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.223 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:29"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:30.522Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 7.563 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:30"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:30.533Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 12.170 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:34.830Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.266 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:34.833Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.771 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:30:34.835Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:30:34"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:30:34"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 18.469 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:34"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:35.513Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.392 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:35"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:35.517Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.070 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:35"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:39.834Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.005 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:39"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:39.839Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 7.678 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:39"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:40.507Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.117 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:40"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:40.510Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.809 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:40"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:44.823Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.519 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:44"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:44.825Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.453 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:44"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:45.517Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.113 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:45"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:45.522Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.079 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:45"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:49.830Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.926 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:49"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:49.833Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.303 ms - 184\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:49"}
{"level":"info","message":"User disconnected: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) has no remaining connections","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:30:50.090Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User undefined (undefined) at 2025-08-02T09:30:50.102Z","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 16.888 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:30:50.131Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User undefined (undefined) at 2025-08-02T09:30:50.175Z","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 54.322 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:30:50.188Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User undefined (undefined) at 2025-08-02T09:30:50.193Z","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 17.222 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"level":"info","message":"Cleared socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) - session remains active","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for EMMANUEL AMOAKOH (AUDIT) - disconnect event","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-02T09:30:50.242Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User undefined (undefined) at 2025-08-02T09:30:50.246Z","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 10.370 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:50.516Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.488 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:50.520Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.917 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:30:50.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 50.335 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:50"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:55.509Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.960 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:55"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:30:55.513Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.161 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:30:55"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:00.522Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.246 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:00"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:00.526Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.266 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:00"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:05.513Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.648 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:05"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:05.516Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.331 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:05"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:10.509Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.426 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:10"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:10.511Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.868 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:10"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:15.521Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.961 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:15"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:15.524Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.621 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:15"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.311 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:20"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:20.518Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.651 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:20"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:20.521Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.393 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:20"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:31:20.525Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:31:20"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:31:20"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 21.752 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:20"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:25.509Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.898 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:25"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:25.512Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.778 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:25"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:30.504Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.902 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:30"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:30.509Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.529 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:30"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:35.515Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.402 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:35"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:35.520Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.550 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:35"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:40.512Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.810 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:40"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:40.516Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.768 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:40"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:45.518Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 7.761 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:45"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:45.533Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 17.750 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:45"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:50.519Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.405 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:50"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:50.520Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.482 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:50"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:31:50.528Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:31:50"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:31:50"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 118.503 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:50"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:55.513Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.053 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:55"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:31:55.517Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.870 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:55"}
{"ip":"**************","level":"info","message":"API Request: GET /vouchers?timestamp=1754083890515","service":"vms-server","timestamp":"2025-08-02T09:31:59.238Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?timestamp=1754083890515","service":"vms-server","timestamp":"2025-08-02 09:31:59"}
{"level":"info","message":"GET /vouchers request at 2025-08-02T09:31:59.252Z by user SELORM (AUDIT), query department: none","service":"vms-server","timestamp":"2025-08-02 09:31:59"}
{"level":"info","message":"Fetching all vouchers for AUDIT user","service":"vms-server","timestamp":"2025-08-02 09:31:59"}
{"level":"info","message":"Found 16 vouchers for request from SELORM (AUDIT)","service":"vms-server","timestamp":"2025-08-02 09:31:59"}
{"level":"info","message":"Sample vouchers: PSOAUG0003-RETURN-COPY (6ef1e089-b534-43a1-87fb-52a2e3424975): VOUCHER RETURNED, PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, PSOAUG0003-PENTSOS-RETURN-COPY (c5554dcf-5831-462c-a702-b61b3a078813): VOUCHER RETURNED","service":"vms-server","timestamp":"2025-08-02 09:31:59"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":true,"service":"vms-server","status":"VOUCHER RETURNED","timestamp":"2025-08-02 09:31:59","voucherId":"PSOAUG0003-RETURN-COPY"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?timestamp=1754083890515 \u001b[32m200\u001b[0m 20.785 ms - 25832\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:31:59"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:00.506Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.463 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:00"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:00.509Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.660 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:00"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:05.517Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.323 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:05"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:05.520Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.265 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:05"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:10.513Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.045 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:10"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:10.516Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.192 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:10"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:15.514Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.763 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:15"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:15.516Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.946 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:15"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:20.507Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.619 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:20"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:20.511Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.642 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:20"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:32:20.514Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:32:20"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:32:20"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 25.342 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:20"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:25.511Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.633 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:25"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:25.514Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.479 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:25"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:30.507Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.781 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:30"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:30.512Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.409 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:30"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:35.505Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.401 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:35"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:35.507Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.777 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:35"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:40.514Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.344 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:40"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:40.515Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.849 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:40"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:45.508Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.086 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:45"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:45.509Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.102 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:45"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:50.505Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.161 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:50"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:50.508Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.892 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:50"}
{"ip":"**************","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02T09:32:50.512Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-02 09:32:50"}
{"level":"info","message":"Found 2 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-02 09:32:50"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 44.961 ms - 343\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:50"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:55.516Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.897 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:55"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:32:55.518Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.595 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:32:55"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:33:00.508Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.360 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:33:00"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:33:00.510Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.371 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:33:00"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-02 09:33:02"}
{"level":"info","message":"🔄 Starting automated backup...","service":"vms-server","timestamp":"2025-08-02 09:33:02"}
{"connections":"1/1 healthy","level":"info","locks":"1 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-02 09:33:02","uptime":"0h"}
{"level":"info","message":"🗑️ Cleaned up old backup: vms_auto_backup_2025-08-02T05-49-26-016Z.sql","service":"vms-server","timestamp":"2025-08-02 09:33:02"}
{"level":"info","message":"✅ Backup cleanup completed: removed 1 old backups","service":"vms-server","timestamp":"2025-08-02 09:33:02"}
{"level":"info","message":"✅ Automated backup completed: vms_auto_backup_2025-08-02T09-33-02-268Z.sql (126742 bytes)","service":"vms-server","timestamp":"2025-08-02 09:33:02"}
{"connections":"1/1 healthy","level":"info","locks":"1 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-02 09:33:02","uptime":"0h"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:33:05.507Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.777 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:33:05"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:33:05.509Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.206 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:33:05"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:33:10.506Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.401 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:33:10"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:33:10.508Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.072 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:33:10"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:33:15.501Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.609 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:33:15"}
{"ip":"**************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-02T09:33:15.505Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.305 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-02 09:33:15"}
