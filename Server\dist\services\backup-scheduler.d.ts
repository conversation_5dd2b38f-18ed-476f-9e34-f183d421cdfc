/**
 * PRODUCTION-GRADE: Automated backup scheduling service
 * Simple, reliable, no external dependencies
 */
export declare class BackupScheduler {
    private backupInterval;
    private dailyBackupTimeout;
    private isBackupInProgress;
    private scheduledTime;
    /**
     * Start automated backup scheduling (DEPRECATED - Use scheduleDaily instead)
     * @param intervalHours - Hours between backups (default: 24 hours)
     */
    start(intervalHours?: number): void;
    /**
     * Schedule daily backup at specific time with random offset
     * @param time - Time in HH:MM format (24-hour)
     * @param randomMinutes - Random offset in minutes (default: 30 minutes)
     */
    scheduleDaily(time: string, randomMinutes?: number): void;
    /**
     * Stop automated backup scheduling
     */
    stop(): void;
    /**
     * Perform automated backup
     */
    private performAutomatedBackup;
    /**
     * Clean up old automated backups (keep last 7)
     */
    private cleanupOldBackups;
    /**
     * Trigger manual backup
     */
    triggerManualBackup(): Promise<boolean>;
    /**
     * Get backup status
     */
    getStatus(): {
        isScheduled: boolean;
        isBackupInProgress: boolean;
        scheduledTime: string | null;
        nextBackup: string;
        backupType: string;
        randomOffset: string;
    };
}
export declare const backupScheduler: BackupScheduler;
