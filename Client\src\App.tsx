import { Toaster as SonnerToaster } from "sonner";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "@/components/theme-provider";
import { useEffect, useState } from "react";
import { useAppStore } from "@/lib/store";
import { initializeDefaultData } from "@/lib/store/slices/initialize-data";
import { initializeGlobalToast } from "@/lib/toast-global";
import { connectSocket, disconnectSocket } from "@/lib/socket";
import { initializeConnectionSystem } from "@/lib/connection-initializer";
import SessionManager from "@/components/SessionManager";
import { YearAwareApp } from "@/components/year-selection/year-aware-app";
import { ErrorBoundary } from "@/components/ErrorBoundary";

import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import AuditDashboard from "./pages/AuditDashboard";
import AuditVoucherView from "./pages/AuditVoucherView";
import AuditAnalytics from "./pages/AuditAnalytics";
import VoucherDetails from "./pages/VoucherDetails";
import ProvisionalCashRecord from "./pages/ProvisionalCashRecord";
import AdminDashboard from "./pages/AdminDashboard";
import MinistriesDashboard from "./pages/MinistriesDashboard";
import PensionsDashboard from "./pages/PensionsDashboard";
import PentmediaDashboard from "./pages/PentmediaDashboard";
import MissionsDashboard from "./pages/MissionsDashboard";
import PentsosDashboard from "./pages/PentsosDashboard";
import Reset from "./reset";
import Register from "./pages/Register";

const queryClient = new QueryClient();

// Initialize app component
const AppContent = () => {
  const { fetchCurrentUser, fetchVouchers, fetchBatches, fetchAllUsers, currentUser, logout } = useAppStore();
  const [isAppInitialized, setIsAppInitialized] = useState(false);

  // Initialize app on mount with debounce to prevent multiple calls
  useEffect(() => {
    let isInitializing = false;

    const initializeApp = async () => {
      if (isInitializing) {
        console.log('App initialization already in progress, skipping...');
        return;
      }

      isInitializing = true;
      console.log('Starting app initialization...');

      try {
        // PRODUCTION-LEVEL: Initialize connection system first for optimal performance
        console.log('🚀 INIT: Starting connection system pre-warming...');
        const connectionInitStart = Date.now();

        // Start connection pre-warming in parallel with other initialization
        const connectionInitPromise = initializeConnectionSystem().then(() => {
          const duration = Date.now() - connectionInitStart;
          console.log(`🚀 INIT: Connection system ready in ${duration}ms`);
        }).catch(error => {
          console.warn('🚀 INIT: Connection pre-warming failed:', error);
        });

        // PRODUCTION: No default data initialization - all data comes from database
        // initializeDefaultData(); // REMOVED: Prevents dummy data initialization

        // Initialize global toast function
        initializeGlobalToast();
        console.log('Global toast function initialized');

        // Wait for connection system to be ready (with timeout)
        await Promise.race([
          connectionInitPromise,
          new Promise(resolve => setTimeout(resolve, 2000)) // 2s timeout
        ]);

        // FIXED: Only try to fetch current user if there might be an active session
        // Check if we have any indication of an active session (like being on a protected route)
        const isOnProtectedRoute = window.location.pathname !== '/';
        let success = false;
        let updatedUser = null;

        if (isOnProtectedRoute) {
          console.log('🔄 App initialization - On protected route, checking for active session...');
          success = await fetchCurrentUser();
          updatedUser = useAppStore.getState().currentUser;
          console.log('🔄 App initialization - fetchCurrentUser result:', { success, updatedUser });
        } else {
          console.log('🔄 App initialization - On login page, skipping fetchCurrentUser');
        }

        // If user is authenticated, connection system is already pre-warmed
        if (success && updatedUser) {
          console.log('🚀 OPTIMIZED: User authenticated with pre-warmed connection:', updatedUser.name, updatedUser.department);

          // PRODUCTION-LEVEL: Connection is already pre-warmed, just activate it
          console.log('🚀 OPTIMIZED: Activating pre-warmed WebSocket connection...');
          try {
            connectSocket(); // This will use the pre-warmed connection
            console.log('✅ OPTIMIZED: Pre-warmed WebSocket connection activated');
          } catch (error) {
            console.error('❌ Failed to activate WebSocket connection:', error);
          }

          // Fetch vouchers based on user department
          if (updatedUser.department === 'AUDIT' || updatedUser.department === 'SYSTEM ADMIN') {
            // Audit and admin see all vouchers
            console.log('🔄 Fetching all vouchers for AUDIT/ADMIN user');
            fetchVouchers();
          } else {
            // Other departments see only their vouchers
            console.log('🔄 Fetching department vouchers for:', updatedUser.department);
            fetchVouchers(updatedUser.department);
          }

        // Force immediate voucher fetch for AUDIT users (CRITICAL FIX)
        if (updatedUser.department === 'AUDIT') {
          console.log('🚨 AUDIT USER DETECTED - Force fetching vouchers immediately');
          setTimeout(() => {
            console.log('🔄 Force fetch vouchers for AUDIT user');
            fetchVouchers();
          }, 100);
        }

          // Fetch users for all authenticated users (needed for dispatch dropdowns)
          console.log('🔄 Calling fetchAllUsers for user:', updatedUser.name);
          try {
            await fetchAllUsers();
            console.log('✅ fetchAllUsers completed successfully for user:', updatedUser.name);
          } catch (usersError) {
            console.error('❌ fetchAllUsers failed:', usersError);
          }

          // Fetch users for all authenticated users (required for dispatch dropdowns)
          console.log('🔄 Calling fetchAllUsers for user:', updatedUser.name);
          try {
            await fetchAllUsers();
            console.log('✅ fetchAllUsers completed successfully for user:', updatedUser.name);
          } catch (usersError) {
            console.error('❌ fetchAllUsers failed:', usersError);
          }

          // Fetch batches for all authenticated users
          console.log('🔄 Calling fetchBatches for user:', updatedUser.name);
          console.log('🔄 fetchBatches function type:', typeof fetchBatches);
          try {
            await fetchBatches();
            console.log('✅ fetchBatches completed successfully for user:', updatedUser.name);
          } catch (batchError) {
            console.error('❌ fetchBatches failed:', batchError);
          }
        } else {
          console.log('❌ App initialization - User not authenticated or fetchCurrentUser failed');
        }

        console.log('App initialization completed');
      } catch (error) {
        console.error('App initialization error:', error);
      } finally {
        isInitializing = false;
        // CRITICAL FIX: Mark app as initialized after authentication attempt
        setIsAppInitialized(true);
      }
    };

    initializeApp();
  }, []); // Empty dependency array - only run once on mount

  // ROBUST BROWSER CLOSE DETECTION: Multi-layered approach with fallbacks
  useEffect(() => {
    if (!currentUser) return;

    console.log('🔧 BROWSER CLOSE: Setting up detection for user:', currentUser.name);

    // Helper function to send logout signal with multiple fallbacks
    const sendLogoutSignal = async (reason: string) => {
      const logoutData = {
        userId: currentUser.id,
        sessionId: currentUser.sessionId,
        reason: reason,
        timestamp: Date.now()
      };

      console.log('📡 LOGOUT SIGNAL: Attempting to send', { reason, userId: currentUser.id });

      // METHOD 1: sendBeacon (most reliable for browser close)
      if (navigator.sendBeacon) {
        try {
          const success = navigator.sendBeacon('/api/auth/immediate-logout', JSON.stringify(logoutData));
          console.log('📡 SENDBEACON:', success ? 'SUCCESS' : 'FAILED', reason);
          if (success) return;
        } catch (error) {
          console.error('❌ SENDBEACON ERROR:', error);
        }
      }

      // METHOD 2: Synchronous fetch (fallback)
      try {
        await fetch('/api/auth/immediate-logout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(logoutData),
          keepalive: true
        });
        console.log('📡 FETCH: SUCCESS', reason);
      } catch (error) {
        console.error('❌ FETCH ERROR:', error);
      }
    };

    // LAYER 1: beforeunload - Traditional approach
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      console.log('🚪 BEFOREUNLOAD: Browser close detected');
      sendLogoutSignal('browser_close');

      // Disconnect WebSocket immediately
      try {
        disconnectSocket();
        console.log('🔌 BEFOREUNLOAD: WebSocket disconnected');
      } catch (error) {
        console.error('❌ WebSocket disconnect error:', error);
      }
    };

    // LAYER 2: pagehide - More reliable than beforeunload
    const handlePageHide = (event: PageTransitionEvent) => {
      console.log('🚪 PAGEHIDE: Triggered', { persisted: event.persisted });

      // Send logout for all pagehide events (navigation or close)
      sendLogoutSignal('page_hide');

      // Disconnect WebSocket
      try {
        disconnectSocket();
        console.log('🔌 PAGEHIDE: WebSocket disconnected');
      } catch (error) {
        console.error('❌ WebSocket disconnect error:', error);
      }
    };

    // LAYER 3: visibilitychange - Detects tab hiding
    const handleVisibilityChange = () => {
      if (document.hidden) {
        console.log('👁️ VISIBILITY: Page became hidden');

        // Immediate signal for hidden state
        sendLogoutSignal('page_hidden');

        // Additional check after delay
        setTimeout(() => {
          if (document.hidden) {
            console.log('🚪 VISIBILITY: Still hidden after 3 seconds');
            sendLogoutSignal('prolonged_hidden');
          }
        }, 3000);
      } else {
        console.log('👁️ VISIBILITY: Page became visible');
      }
    };

    // LAYER 4: unload - Final attempt
    const handleUnload = () => {
      console.log('🚪 UNLOAD: Final unload event');
      sendLogoutSignal('unload');
      disconnectSocket();
    };

    // Add all event listeners
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('pagehide', handlePageHide);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('unload', handleUnload);

    return () => {
      console.log('🧹 CLEANUP: Removing browser close detection listeners');
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('pagehide', handlePageHide);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('unload', handleUnload);
    };
  }, [currentUser]);

  // PRODUCTION FIX: Proper inactivity detection for auto-logout
  useEffect(() => {
    if (!currentUser) return; // Only track activity when user is logged in

    let inactivityTimer: number | undefined;
    const INACTIVITY_TIMEOUT = 60 * 60 * 1000; // 60 minutes (matches server cleanup)

    const resetInactivityTimer = () => {
      if (inactivityTimer) {
        window.clearTimeout(inactivityTimer);
      }

      inactivityTimer = window.setTimeout(async () => {
        console.log('User inactive for 60 minutes, logging out...');
        // Disconnect WebSocket before logout
        disconnectSocket();
        await logout();
        // Store session expiry message for login page
        localStorage.setItem('auth_error', 'Your session expired due to inactivity after 60 minutes. Please log in again.');
        window.location.href = '/';
      }, INACTIVITY_TIMEOUT);
    };

    // Reset timer on user activity
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

    // Start the initial timer
    resetInactivityTimer();

    // Add event listeners for user activity
    activityEvents.forEach(event => {
      window.addEventListener(event, resetInactivityTimer);
    });

    // Cleanup
    return () => {
      if (inactivityTimer) {
        window.clearTimeout(inactivityTimer);
      }

      activityEvents.forEach(event => {
        window.removeEventListener(event, resetInactivityTimer);
      });
    };
  }, [currentUser, logout]);

  return (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="dark" storageKey="voucher-management-theme">
      <TooltipProvider>
        <SonnerToaster
          position="bottom-left"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'var(--background)',
              color: 'var(--foreground)',
              border: '1px solid var(--border)',
            },
          }}
          closeButton
        />
        <Toaster />
        <SessionManager />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Login />} />
            <Route path="/reset" element={<Reset />} />
            <Route path="/register" element={<Register />} />
            <Route path="*" element={
              <YearAwareApp isAppInitialized={isAppInitialized}>
                <Routes>
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/dashboard/voucher/:voucherId" element={<VoucherDetails />} />
                  <Route path="/ministries-dashboard" element={<MinistriesDashboard />} />
                  <Route path="/pensions-dashboard" element={<PensionsDashboard />} />
                  <Route path="/pentmedia-dashboard" element={<PentmediaDashboard />} />
                  <Route path="/missions-dashboard" element={<MissionsDashboard />} />
                  <Route path="/pentsos-dashboard" element={<PentsosDashboard />} />
                  <Route path="/audit-dashboard" element={<AuditDashboard />} />
                  <Route path="/audit-dashboard/voucher/:voucherId" element={<AuditVoucherView />} />
                  <Route path="/audit-dashboard/cash-record" element={<ProvisionalCashRecord />} />
                  <Route path="/audit-dashboard/analytics" element={<AuditAnalytics />} />
                  <Route path="/admin-dashboard" element={<AdminDashboard />} />
                  <Route path="/admin-access" element={<AdminDashboard />} />
                  <Route path="*" element={<Login />} />
                </Routes>
              </YearAwareApp>
            } />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);
};

const App = () => (
  <ErrorBoundary>
    <AppContent />
  </ErrorBoundary>
);

export default App;
