// Script to manually add logout_reason column to active_sessions table
const mysql = require('mysql2/promise');

async function addLogoutReasonColumn() {
  let connection;
  
  try {
    console.log('🔧 Connecting to database...');
    
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // Check if column already exists
    console.log('🔍 Checking if logout_reason column exists...');
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'vms_production' 
      AND TABLE_NAME = 'active_sessions' 
      AND COLUMN_NAME = 'logout_reason'
    `);
    
    if (columns.length > 0) {
      console.log('✅ logout_reason column already exists');
      return;
    }
    
    console.log('➕ Adding logout_reason column...');
    await connection.execute(`
      ALTER TABLE active_sessions 
      ADD COLUMN logout_reason VARCHAR(50) DEFAULT NULL
    `);
    
    console.log('✅ logout_reason column added successfully!');
    
    // Verify the column was added
    const [verifyColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'vms_production' 
      AND TABLE_NAME = 'active_sessions' 
      AND COLUMN_NAME = 'logout_reason'
    `);
    
    if (verifyColumns.length > 0) {
      console.log('✅ Verification successful - column exists');
    } else {
      console.log('❌ Verification failed - column not found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

addLogoutReasonColumn();
