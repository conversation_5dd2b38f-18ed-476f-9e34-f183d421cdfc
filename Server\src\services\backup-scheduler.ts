import fs from 'fs';
import path from 'path';
import { logger } from '../utils/logger.js';
import { query } from '../database/db.js';

/**
 * PRODUCTION-GRADE: Automated backup scheduling service
 * Simple, reliable, no external dependencies
 */
export class BackupScheduler {
  private backupInterval: NodeJS.Timeout | null = null;
  private dailyBackupTimeout: NodeJS.Timeout | null = null;
  private isBackupInProgress = false;
  private scheduledTime: string | null = null; // Format: "HH:MM"

  /**
   * Start automated backup scheduling (DEPRECATED - Use scheduleDaily instead)
   * @param intervalHours - Hours between backups (default: 24 hours)
   */
  start(intervalHours: number = 24) {
    // INDUSTRY STANDARD: Use time-based scheduling instead of interval-based
    logger.warn('⚠️ Interval-based backup scheduling is deprecated. Use scheduleDaily() for production.');
    logger.info('🔄 Backup scheduler initialized - waiting for admin configuration');

    // Don't start any automatic backups - wait for admin to configure schedule
    // This follows industry best practice of admin-controlled backup timing
  }

  /**
   * Schedule daily backup at specific time with random offset
   * @param time - Time in HH:MM format (24-hour)
   * @param randomMinutes - Random offset in minutes (default: 30 minutes)
   */
  scheduleDaily(time: string, randomMinutes: number = 30) {
    // Validate time format
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(time)) {
      throw new Error('Invalid time format. Use HH:MM (24-hour format)');
    }

    this.stop();
    this.scheduledTime = time;

    const [hours, minutes] = time.split(':').map(Number);

    const scheduleNextBackup = () => {
      const now = new Date();
      const scheduledDate = new Date();
      scheduledDate.setHours(hours, minutes, 0, 0);

      // INDUSTRY STANDARD: Add random offset to prevent system load spikes
      // Random offset between -randomMinutes and +randomMinutes
      const randomOffset = Math.floor(Math.random() * (randomMinutes * 2 + 1)) - randomMinutes;
      scheduledDate.setMinutes(scheduledDate.getMinutes() + randomOffset);

      // If the scheduled time has passed today, schedule for tomorrow
      if (scheduledDate <= now) {
        scheduledDate.setDate(scheduledDate.getDate() + 1);
        // Recalculate random offset for tomorrow
        const newRandomOffset = Math.floor(Math.random() * (randomMinutes * 2 + 1)) - randomMinutes;
        scheduledDate.setMinutes(minutes + newRandomOffset);
      }

      const msUntilBackup = scheduledDate.getTime() - now.getTime();

      logger.info(`🕒 Next automated backup scheduled for: ${scheduledDate.toLocaleString()} (±${randomMinutes}min random offset)`);

      this.dailyBackupTimeout = setTimeout(async () => {
        await this.performAutomatedBackup();
        // Schedule the next backup for tomorrow
        scheduleNextBackup();
      }, msUntilBackup);
    };

    scheduleNextBackup();
    logger.info(`✅ Daily backup scheduled at ${time} every day (±${randomMinutes}min random offset)`);
  }

  /**
   * Stop automated backup scheduling
   */
  stop() {
    if (this.backupInterval) {
      clearInterval(this.backupInterval);
      this.backupInterval = null;
    }
    if (this.dailyBackupTimeout) {
      clearTimeout(this.dailyBackupTimeout);
      this.dailyBackupTimeout = null;
    }
    this.scheduledTime = null;
    logger.info('🛑 Automated backup scheduler stopped');
  }

  /**
   * Perform automated backup
   */
  private async performAutomatedBackup(): Promise<boolean> {
    if (this.isBackupInProgress) {
      logger.warn('⚠️ Backup already in progress, skipping scheduled backup');
      return false;
    }

    this.isBackupInProgress = true;
    
    try {
      logger.info('🔄 Starting automated backup...');
      
      // Create backups directory if it doesn't exist
      const backupsDir = path.join(process.cwd(), 'backups');
      if (!fs.existsSync(backupsDir)) {
        fs.mkdirSync(backupsDir, { recursive: true });
      }
      
      // Generate backup filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFilename = `vms_auto_backup_${timestamp}.sql`;
      const backupPath = path.join(backupsDir, backupFilename);
      
      // Database configuration
      const dbConfig = {
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '3306'),
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || 'vms@2025@1989',
        database: process.env.DB_NAME || 'vms_production',
      };
      
      // Create database connection for backup
      const mysql = require('mysql2/promise');
      const connection = await mysql.createConnection(dbConfig);
      
      // Get all table names
      const [tables] = await connection.execute('SHOW TABLES');
      const tableNames = tables.map((row: any) => Object.values(row)[0]);
      
      let backupContent = `-- VMS Production Database Backup (AUTOMATED)\n`;
      backupContent += `-- Generated: ${new Date().toISOString()}\n`;
      backupContent += `-- Database: ${dbConfig.database}\n`;
      backupContent += `-- Type: Automated Backup\n\n`;
      backupContent += `SET FOREIGN_KEY_CHECKS = 0;\n\n`;
      
      // Backup each table
      for (const tableName of tableNames) {
        // Get table structure
        const [createTable] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
        const createStatement = (createTable as any)[0]['Create Table'];
        
        backupContent += `-- Table: ${tableName}\n`;
        backupContent += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
        backupContent += `${createStatement};\n\n`;
        
        // Get table data
        const [rows] = await connection.execute(`SELECT * FROM \`${tableName}\``);
        if ((rows as any[]).length > 0) {
          backupContent += `-- Data for table: ${tableName}\n`;
          backupContent += `INSERT INTO \`${tableName}\` VALUES\n`;
          
          const values = (rows as any[]).map(row => {
            const escapedValues = Object.values(row).map(value => {
              if (value === null) return 'NULL';
              if (typeof value === 'string') {
                // Handle datetime strings that might be in ISO format
                if (value.includes('T') && value.includes('Z')) {
                  try {
                    // Convert ISO datetime to MySQL format
                    const date = new Date(value);
                    // Check if the date is valid
                    if (isNaN(date.getTime())) {
                      // If invalid date, treat as regular string
                      return `'${value.replace(/'/g, "''")}'`;
                    }
                    return `'${date.toISOString().slice(0, 19).replace('T', ' ')}'`;
                  } catch (error) {
                    // If any error occurs, treat as regular string
                    return `'${value.replace(/'/g, "''")}'`;
                  }
                }
                return `'${value.replace(/'/g, "''")}'`;
              }
              if (value instanceof Date) {
                try {
                  // Check if the date is valid before converting
                  if (isNaN(value.getTime())) {
                    return 'NULL';
                  }
                  return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
                } catch (error) {
                  return 'NULL';
                }
              }
              return value;
            });
            return `(${escapedValues.join(', ')})`;
          });
          
          backupContent += values.join(',\n') + ';\n\n';
        }
      }
      
      backupContent += `SET FOREIGN_KEY_CHECKS = 1;\n`;
      
      // Write backup file
      fs.writeFileSync(backupPath, backupContent, 'utf8');
      
      // Verify backup file was created and has content
      const stats = fs.statSync(backupPath);
      if (stats.size === 0) {
        throw new Error('Backup file is empty');
      }
      
      await connection.end();
      
      // Update last backup date in system settings
      await query(
        'UPDATE system_settings SET last_backup_date = ? WHERE id = 1',
        [new Date().toISOString()]
      );
      
      // Clean up old backups (keep last 7 automated backups)
      await this.cleanupOldBackups(backupsDir);
      
      logger.info(`✅ Automated backup completed: ${backupFilename} (${stats.size} bytes)`);
      return true;
      
    } catch (error) {
      logger.error('❌ Automated backup failed:', error);
      return false;
    } finally {
      this.isBackupInProgress = false;
    }
  }

  /**
   * Clean up old automated backups (keep last 7)
   */
  private async cleanupOldBackups(backupsDir: string) {
    try {
      const files = fs.readdirSync(backupsDir)
        .filter(file => file.startsWith('vms_auto_backup_') && file.endsWith('.sql'))
        .map(file => ({
          name: file,
          path: path.join(backupsDir, file),
          stats: fs.statSync(path.join(backupsDir, file))
        }))
        .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime()); // Sort by date, newest first

      // Keep only the 7 most recent automated backups
      const filesToDelete = files.slice(7);
      
      for (const file of filesToDelete) {
        fs.unlinkSync(file.path);
        logger.info(`🗑️ Cleaned up old backup: ${file.name}`);
      }
      
      if (filesToDelete.length > 0) {
        logger.info(`✅ Backup cleanup completed: removed ${filesToDelete.length} old backups`);
      }
      
    } catch (error) {
      logger.warn('⚠️ Backup cleanup failed:', error);
    }
  }

  /**
   * Trigger manual backup
   */
  async triggerManualBackup(): Promise<boolean> {
    return await this.performAutomatedBackup();
  }

  /**
   * Get backup status
   */
  getStatus() {
    const isScheduled = this.backupInterval !== null || this.dailyBackupTimeout !== null;
    let nextBackup = 'Not scheduled';
    let backupType = 'None';

    if (this.scheduledTime && this.dailyBackupTimeout) {
      const now = new Date();
      const [hours, minutes] = this.scheduledTime.split(':').map(Number);
      const scheduledDate = new Date();
      scheduledDate.setHours(hours, minutes, 0, 0);

      if (scheduledDate <= now) {
        scheduledDate.setDate(scheduledDate.getDate() + 1);
      }

      nextBackup = `${scheduledDate.toLocaleString()} (±30min random)`;
      backupType = 'Daily with random timing';
    } else if (this.backupInterval) {
      nextBackup = 'Interval-based (deprecated)';
      backupType = 'Interval-based';
    }

    return {
      isScheduled,
      isBackupInProgress: this.isBackupInProgress,
      scheduledTime: this.scheduledTime,
      nextBackup,
      backupType,
      randomOffset: this.scheduledTime ? '±30 minutes' : 'N/A'
    };
  }
}

// Export singleton instance
export const backupScheduler = new BackupScheduler();
