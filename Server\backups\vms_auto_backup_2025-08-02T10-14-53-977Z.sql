-- VMS Production Database Backup (AUTOMATED)
-- Generated: 2025-08-02T10:14:53.997Z
-- Database: vms_production
-- Type: Automated Backup

SET FOREIGN_KEY_CHECKS = 0;

-- Table: active_sessions
DROP TABLE IF EXISTS `active_sessions`;
CREATE TABLE `active_sessions` (
  `id` varchar(100) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `token` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` datetime DEFAULT ((now() + interval 24 hour)),
  `user_name` varchar(255) DEFAULT NULL,
  `department` varchar(50) DEFAULT NULL,
  `socket_id` varchar(100) DEFAULT NULL,
  `session_start` datetime NOT NULL,
  `last_activity` datetime NOT NULL,
  `session_end` datetime DEFAULT NULL,
  `client_ip` varchar(50) DEFAULT NULL,
  `user_agent` text,
  `is_active` tinyint(1) DEFAULT '1',
  `role` varchar(50) DEFAULT 'User',
  `selected_year` int DEFAULT NULL,
  `selected_database` varchar(100) DEFAULT NULL,
  `logout_reason` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `is_active` (`is_active`),
  KEY `department` (`department`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: active_sessions
INSERT INTO `active_sessions` VALUES
('08eaed03-0789-4652-81f2-6861adb609e0', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 14:31:07', '2025-08-02 14:31:07', 'CHARIS', 'MISSIONS', NULL, '2025-08-01 14:31:07', '2025-08-01 14:31:33', '2025-08-01 14:31:33', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('0cdcb5c2-1ec8-405d-bc27-f69611022582', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:11:35', '2025-08-02 13:11:35', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 13:11:35', '2025-08-01 13:17:37', '2025-08-01 13:27:28', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('0df6a512-264e-4f55-8873-b7a15daf8a84', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:17:37', '2025-08-02 13:17:37', 'EMMANUEL AMOAKOH', 'AUDIT', 'GdruNpMp1uZGgsG_AAAF', '2025-08-01 13:17:37', '2025-08-01 13:21:32', '2025-08-01 13:21:32', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('10c3c58c-eb03-47fa-9283-8ce172a3cf21', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', NULL, '2025-08-02 08:23:20', '2025-08-03 08:23:20', 'MR. FELIX AYISI', 'FINANCE', NULL, '2025-08-02 08:23:20', '2025-08-02 08:43:31', '2025-08-02 09:51:24', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('1102df72-c4b0-4f69-9284-59bc11487d07', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-02 09:25:06', '2025-08-03 09:25:06', 'SELORM', 'AUDIT', NULL, '2025-08-02 09:25:06', '2025-08-02 09:25:54', '2025-08-02 09:34:00', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('12973538-3b6f-4d4d-834a-f8e61e578911', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', NULL, '2025-08-02 06:39:35', '2025-08-03 06:39:35', 'MR. FELIX AYISI', 'FINANCE', NULL, '2025-08-02 06:39:35', '2025-08-02 06:39:53', '2025-08-02 06:39:53', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('1707f9d8-2c55-4863-875c-fa522c93ea40', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 18:08:31', '2025-08-02 18:08:31', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 18:08:31', '2025-08-01 19:01:46', '2025-08-01 19:07:12', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('213ade58-5829-4905-a88b-db6e1e947fa5', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 14:45:26', '2025-08-02 14:45:26', 'CHARIS', 'MISSIONS', NULL, '2025-08-01 14:45:26', '2025-08-01 14:45:32', '2025-08-01 14:45:32', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('24810e60-0184-48e6-9ac2-a123ff2d822f', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 06:05:59', '2025-08-03 06:05:59', 'EMMANUEL AMOAKOH', 'AUDIT', 'UO9EZ-_j4l08i0BDAAAQ', '2025-08-02 06:05:59', '2025-08-02 06:40:02', '2025-08-02 06:40:20', NULL, NULL, 0, 'User', 2025, 'vms_production', NULL),
('2bfe9942-e501-4862-8917-14bc083456fd', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 16:12:40', '2025-08-02 16:12:40', 'CHARIS', 'MISSIONS', NULL, '2025-08-01 16:12:40', '2025-08-01 16:21:26', '2025-08-01 16:21:26', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('2c663164-0c0c-4c8e-bbbd-92dc93a7c17c', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 18:08:42', '2025-08-02 18:08:42', 'CHARIS', 'MISSIONS', NULL, '2025-08-01 18:08:42', '2025-08-01 19:11:23', '2025-08-02 05:44:56', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('2fb8c041-b7e6-4943-8ff6-6c0fe995703d', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-01 15:23:49', '2025-08-02 15:23:49', 'SELORM', 'AUDIT', NULL, '2025-08-01 15:23:49', '2025-08-01 15:28:49', '2025-08-01 15:28:49', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('33179466-2878-4d89-b10c-0b9bab5677ed', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 15:47:36', '2025-08-02 15:47:36', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 15:47:36', '2025-08-01 15:52:49', '2025-08-01 16:02:11', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('3a013862-294d-44cc-bb08-5d34c3204e42', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 14:26:48', '2025-08-02 14:26:48', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 14:26:48', '2025-08-01 14:31:07', '2025-08-01 14:44:38', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('42fda752-5924-4176-bc7e-a48db8ed4e12', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 15:19:07', '2025-08-02 15:19:07', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 15:19:07', '2025-08-01 15:23:29', '2025-08-01 15:23:29', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('4b357bbf-1016-4768-9a7b-1cbe3556dfc6', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-02 08:43:31', '2025-08-03 08:43:31', 'SELORM', 'AUDIT', NULL, '2025-08-02 08:43:31', '2025-08-02 08:45:57', '2025-08-02 08:45:57', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('4d06bf73-6c94-4ede-baa5-f24e06508613', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 14:31:40', '2025-08-02 14:31:40', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 14:31:40', '2025-08-01 14:45:26', '2025-08-01 14:54:08', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('4f321baa-ed8a-423c-8261-1cb7846575f4', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:22:19', '2025-08-02 13:22:19', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 13:22:19', '2025-08-01 13:29:35', '2025-08-01 13:36:58', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('5413a590-cd3c-424c-a348-fdc670e48551', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 19:46:30', '2025-08-02 19:46:30', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 19:46:30', '2025-08-01 19:56:21', '2025-08-01 19:56:21', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('541da564-7d2a-48be-b40b-372798e654e1', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:33:28', '2025-08-02 13:33:28', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 13:33:28', '2025-08-01 14:26:48', '2025-08-01 14:26:59', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('5703dc5e-b426-43b5-b9d8-0a93643eb3d0', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 09:01:55', '2025-08-03 09:01:55', 'EMMANUEL AMOAKOH', 'AUDIT', 'ZFDoFwZEzN4CehhdAAAU', '2025-08-02 09:01:55', '2025-08-02 09:12:23', '2025-08-02 09:12:23', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('5ab9ee5e-7cc7-48dd-afa2-8275e4ab66a0', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 09:24:30', '2025-08-03 09:24:30', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 09:24:30', '2025-08-02 09:26:58', '2025-08-02 09:26:58', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('5c15d271-16a6-4d8a-b5cf-e3ae43379a53', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-02 08:46:56', '2025-08-03 08:46:56', 'SELORM', 'AUDIT', NULL, '2025-08-02 08:46:56', '2025-08-02 08:47:25', '2025-08-02 09:05:09', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('68007fe8-763c-4b89-a25a-89e78ed98cc7', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:21:45', '2025-08-02 13:21:45', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 13:21:45', '2025-08-01 13:23:34', '2025-08-01 13:22:09', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('6a7b19f6-f077-497a-b3b5-df76cfc76abe', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', NULL, '2025-08-01 19:56:34', '2025-08-02 19:56:34', 'MR. FELIX AYISI', 'FINANCE', NULL, '2025-08-01 19:56:34', '2025-08-01 19:57:12', '2025-08-01 19:57:12', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('6b252c54-cd5e-4828-845b-996a68a10bab', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 15:53:22', '2025-08-02 15:53:22', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 15:53:22', '2025-08-01 17:38:34', '2025-08-01 17:38:34', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('6cc10e86-85dc-4ad4-8e89-b48a61e547cf', '97a5561d-13c5-4692-be66-5dddcc6819fc', NULL, '2025-08-02 06:54:47', '2025-08-03 06:54:47', 'JERRY JOHN', 'PENSIONS', NULL, '2025-08-02 06:54:47', '2025-08-02 07:16:51', '2025-08-02 07:16:51', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('70d06a0d-9cfb-492d-b684-c068ec5f4e54', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-02 07:17:01', '2025-08-03 07:17:01', 'CHARIS', 'MISSIONS', NULL, '2025-08-02 07:17:01', '2025-08-02 07:17:11', '2025-08-02 07:17:11', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('719c8684-20cb-432e-a1a0-00ee3e5aae1a', 'admin-default', NULL, '2025-08-02 08:17:49', '2025-08-03 08:17:49', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-08-02 08:17:49', '2025-08-02 08:19:39', '2025-08-02 08:19:39', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('72f3e64a-c360-4a89-957e-a406776d9a9c', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 19:57:20', '2025-08-02 19:57:20', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 19:57:20', '2025-08-01 20:04:12', '2025-08-01 20:04:12', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('7698ae21-61f5-4ff0-9f1a-2c1a5cdaa433', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 09:52:57', '2025-08-03 09:52:57', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 09:52:57', '2025-08-02 09:53:02', '2025-08-02 09:53:02', NULL, NULL, 0, 'User', NULL, NULL, 'browser_close'),
('78969a8c-89ce-4e3e-82f3-ce6bfed14fc7', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 06:40:02', '2025-08-03 06:40:02', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 06:40:02', '2025-08-02 06:40:24', '2025-08-02 06:40:24', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('790cefbd-b6a0-4b1b-bd8b-19e90eaab25a', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-02 10:11:51', '2025-08-03 10:11:51', 'SELORM', 'AUDIT', NULL, '2025-08-02 10:11:51', '2025-08-02 10:14:51', NULL, NULL, NULL, 1, 'User', NULL, NULL, NULL),
('791d074a-6886-4130-99a6-3cf7424ebe40', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-02 09:25:54', '2025-08-03 09:25:54', 'SELORM', 'AUDIT', NULL, '2025-08-02 09:25:54', '2025-08-02 09:27:07', '2025-08-02 09:27:07', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('7c73e7d8-87ef-4867-bd85-f72fb5ad0390', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 20:00:04', '2025-08-02 20:00:04', 'CHARIS', 'MISSIONS', NULL, '2025-08-01 20:00:04', '2025-08-01 20:03:38', '2025-08-01 20:03:38', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('7e051eed-6d30-4c36-bae6-0dd36d95894d', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 09:44:18', '2025-08-03 09:44:18', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 09:44:18', '2025-08-02 09:46:04', '2025-08-02 09:46:04', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('7e6daa0e-8d8f-4745-a024-c773d81b1090', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-02 09:02:07', '2025-08-03 09:02:07', 'SELORM', 'AUDIT', NULL, '2025-08-02 09:02:07', '2025-08-02 09:25:06', '2025-08-02 09:28:32', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('7fa76f6c-750e-4912-90bd-fa53e8bff3b8', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 20:03:52', '2025-08-02 20:03:52', 'CHARIS', 'MISSIONS', NULL, '2025-08-01 20:03:52', '2025-08-01 20:04:16', '2025-08-01 20:04:16', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('8933839d-f805-4278-bdb3-33eca8d3b507', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 09:46:51', '2025-08-03 09:46:51', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 09:46:51', '2025-08-02 09:59:27', NULL, NULL, NULL, 1, 'User', NULL, NULL, NULL),
('92121337-9fb4-427c-96e4-8182628c394c', '24f05c6c-a7d4-49c6-a1b1-3649e5f473cb', NULL, '2025-08-02 07:17:22', '2025-08-03 07:17:22', 'GYAMPOH', 'PENTSOS', NULL, '2025-08-02 07:17:22', '2025-08-02 07:29:39', '2025-08-02 07:29:39', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('939fc5e5-b28c-4971-b32a-045fdddb0d39', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-02 09:44:32', '2025-08-03 09:44:32', 'SELORM', 'AUDIT', NULL, '2025-08-02 09:44:32', '2025-08-02 09:52:08', '2025-08-02 09:52:08', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('9b89eac4-a197-4b72-a581-f316a6ff70ae', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 12:14:28', '2025-08-02 12:14:28', 'CHARIS', 'MISSIONS', 'VAGA-j27r-tngnxQAAAT', '2025-08-01 12:14:28', '2025-08-01 12:34:56', '2025-08-01 12:34:56', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('a3a9cb31-591c-4d65-bf0f-5f4154e2c216', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 08:43:16', '2025-08-03 08:43:16', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 08:43:16', '2025-08-02 08:54:32', '2025-08-02 08:54:32', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('a3b871a9-6c06-460c-a4e7-ad814d523dc4', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-02 09:29:20', '2025-08-03 09:29:20', 'SELORM', 'AUDIT', NULL, '2025-08-02 09:29:20', '2025-08-02 09:35:44', '2025-08-02 09:34:19', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('a41a6a98-17d5-45e2-acf4-6ffa157e3253', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 12:14:42', '2025-08-02 12:14:42', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 12:14:42', '2025-08-01 12:34:51', '2025-08-01 12:34:51', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('a5c6e2a1-c980-426a-a256-2a7f8a70a8a9', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', NULL, '2025-08-02 05:44:59', '2025-08-03 05:44:59', 'MR. FELIX AYISI', 'FINANCE', 'TCQKAntNyZvJtd-QAABB', '2025-08-02 05:44:59', '2025-08-02 06:39:53', '2025-08-02 06:52:38', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('a83bcf0f-52c0-4ba3-96d8-5958a8b4a54c', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 06:53:11', '2025-08-03 06:53:11', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 06:53:11', '2025-08-02 07:29:44', '2025-08-02 07:29:44', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('acf09fef-74e3-4079-8532-52ec8ad38154', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 09:58:07', '2025-08-03 09:58:07', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 09:58:07', '2025-08-02 09:58:20', '2025-08-02 09:58:17', NULL, NULL, 0, 'User', NULL, NULL, 'page_hidden'),
('b73d25b1-2be3-4089-a1f8-21e261179041', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', NULL, '2025-08-02 06:40:34', '2025-08-03 06:40:34', 'MR. FELIX AYISI', 'FINANCE', NULL, '2025-08-02 06:40:34', '2025-08-02 06:54:37', '2025-08-02 06:54:37', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('b862b2c6-e850-4f17-86ab-ff9a92c9e191', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 09:29:34', '2025-08-03 09:29:34', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 09:29:34', '2025-08-02 09:36:24', '2025-08-02 09:51:24', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('ba70e61a-071d-42eb-8618-2c0acebf740c', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:29:35', '2025-08-02 13:29:35', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 13:29:35', '2025-08-01 13:33:28', '2025-08-01 13:36:58', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('c659e91d-2584-45bf-a0a2-a176b31137b4', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 07:30:48', '2025-08-03 07:30:48', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 07:30:48', '2025-08-02 08:16:49', '2025-08-02 08:16:49', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('cbd07dde-5aaf-47e3-be9d-164d9aaed2da', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 14:45:41', '2025-08-02 14:45:41', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 14:45:41', '2025-08-01 15:19:07', '2025-08-01 15:23:23', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('cc051763-d599-4ef6-bffb-2b72171d4283', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 17:41:02', '2025-08-02 17:41:02', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 17:41:02', '2025-08-01 18:09:39', '2025-08-01 18:08:14', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('cc9aa47f-4a18-4e96-98ca-75568de3a01d', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 14:48:55', '2025-08-02 14:48:55', 'CHARIS', 'MISSIONS', NULL, '2025-08-01 14:48:55', '2025-08-01 15:47:15', '2025-08-01 15:52:41', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('d1da5551-0c50-450a-8a04-3f77fe2ee2e8', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 09:59:27', '2025-08-03 09:59:27', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 09:59:27', '2025-08-02 10:01:04', '2025-08-02 10:01:04', NULL, NULL, 0, 'User', NULL, NULL, 'browser_close'),
('d59dcdb9-29db-4279-a0c8-5b25623dfc13', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-02 09:35:35', '2025-08-03 09:35:35', 'SELORM', 'AUDIT', 'HDBpQrr-KBGVEEsoAAA-', '2025-08-02 09:35:35', '2025-08-02 09:39:49', '2025-08-02 09:39:49', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('d7b5c3f0-7c7f-45c0-a820-a95043b18399', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 10:12:02', '2025-08-03 10:12:02', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 10:12:02', '2025-08-02 10:12:40', '2025-08-02 10:12:40', NULL, NULL, 0, 'User', NULL, NULL, 'page_hidden'),
('d8843a54-435c-49a6-91b8-888eb9b4e0e3', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 19:01:40', '2025-08-02 19:01:40', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 19:01:40', '2025-08-01 19:46:30', '2025-08-01 19:55:00', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('da5f224c-76b8-42f0-8813-cd6bea59412b', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 09:35:52', '2025-08-03 09:35:52', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-02 09:35:52', '2025-08-02 09:36:20', '2025-08-02 09:36:20', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('e5e364ea-b430-48b4-92e6-4f99d7f72454', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 13:11:58', '2025-08-02 13:11:58', 'CHARIS', 'MISSIONS', 'aWmoirp4VKG6iWmsAAAT', '2025-08-01 13:11:58', '2025-08-01 13:29:07', '2025-08-01 13:29:07', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('e67263a9-f41b-4d76-9fdc-8a59aaeaf188', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-01 19:40:03', '2025-08-02 19:40:03', 'SELORM', 'AUDIT', NULL, '2025-08-01 19:40:03', '2025-08-01 19:59:53', '2025-08-01 19:59:53', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('ec121924-bd8b-45ec-8e39-e885c226635d', '97a5561d-13c5-4692-be66-5dddcc6819fc', NULL, '2025-08-02 06:23:43', '2025-08-03 06:23:43', 'JERRY JOHN', 'PENSIONS', NULL, '2025-08-02 06:23:43', '2025-08-02 06:40:50', '2025-08-02 06:39:25', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('f03442b7-4ccd-40c2-95aa-dd5bff0ce745', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-02 09:53:13', '2025-08-03 09:53:13', 'SELORM', 'AUDIT', '7_JqWw3rZd_DW8LcAAAJ', '2025-08-02 09:53:13', '2025-08-02 10:04:43', '2025-08-02 10:08:17', NULL, NULL, 0, 'User', NULL, NULL, 'browser_close'),
('f128e89e-5ab6-4765-a7af-de427d270642', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-02 09:52:36', '2025-08-03 09:52:36', 'EMMANUEL AMOAKOH', 'AUDIT', 'm8Njk3DPf8dFSK8kAAAg', '2025-08-02 09:52:36', '2025-08-02 09:53:06', '2025-08-02 09:53:25', NULL, NULL, 0, 'User', NULL, NULL, 'page_hidden'),
('f12a75df-ae2e-424e-a0d3-1da7deb45505', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 15:47:14', '2025-08-02 15:47:14', 'CHARIS', 'MISSIONS', NULL, '2025-08-01 15:47:14', '2025-08-01 16:12:40', '2025-08-01 16:12:51', NULL, NULL, 0, 'User', NULL, NULL, NULL),
('f195667b-b462-4c16-b1c7-79e4f3e06e99', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', NULL, '2025-08-01 20:05:22', '2025-08-02 20:05:22', 'MR. FELIX AYISI', 'FINANCE', NULL, '2025-08-01 20:05:22', '2025-08-02 05:44:59', '2025-08-02 05:54:26', NULL, NULL, 0, 'User', NULL, NULL, NULL);

-- Table: audit_logs
DROP TABLE IF EXISTS `audit_logs`;
CREATE TABLE `audit_logs` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `user_name` varchar(255) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `action` varchar(255) NOT NULL,
  `description` text,
  `resource_type` varchar(50) NOT NULL,
  `resource_id` varchar(36) DEFAULT NULL,
  `details` text,
  `timestamp` datetime NOT NULL,
  `ip_address` varchar(50) DEFAULT NULL,
  `user_agent` text,
  `severity` varchar(50) DEFAULT 'INFO',
  PRIMARY KEY (`id`),
  KEY `idx_audit_logs_user_id` (`user_id`),
  KEY `idx_audit_logs_timestamp` (`timestamp`),
  KEY `idx_audit_logs_resource` (`resource_type`,`resource_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: audit_logs
INSERT INTO `audit_logs` VALUES
('01ce094c-1f6e-4220-a11b-33c552841034', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:12:23', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('02719160-c8ac-4891-ac9a-5683bf68c45e', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 19:46:30', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('0517f5cb-f7bb-4944-91b1-d91c151cfbc3', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:36:20', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('05213437-551c-40e2-9063-463542f67ba2', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 14:31:33', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('0621315f-31c8-421f-a281-b665fedb5ba6', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 08:16:49', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('076568fb-7ef9-4073-814b-2a67dc52a15d', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:26:58', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('07fc17ee-58da-42d8-9cdd-eb1a20a21ac5', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:11:35', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('08ffc86a-3e3a-4b2c-a518-39cd97886258', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 15:23:29', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('0b5a484c-f48f-427e-a1be-a2ce8558e892', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 14:48:55', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('0c520345-5708-40df-b0ed-8528ec1d0e67', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 16:21:26', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('0cc96c03-d534-4390-b44c-6450719be84a', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 14:45:26', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('0eb09b61-a711-4c06-8ab7-93e1e10dcf94', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 07:29:44', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('158a8292-daf3-46e6-9e8d-53e4d9d88923', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 13:11:58', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('18bdf4b9-3f7d-4b3b-83b1-e33410702cd3', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 20:03:52', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('1d9e4864-7800-4892-ab88-7d3ec17debcf', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 20:04:12', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('1e202655-ef2e-4cf7-a6b6-871c0ce8491b', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 09:25:06', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('1ff80676-67e8-4ed2-a53c-9ea912906a04', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 19:01:40', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('208c4d4d-4f7f-48fe-9a30-b2cf99544ac9', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', 'FINANCE', 'LOGIN', 'MR. FELIX AYISI logged in to FINANCE department', 'USER', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', '{}', '2025-08-01 20:05:22', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('245dba1e-96a7-41d1-8b12-d8ffd852efb3', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 10:11:51', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('26c61bce-8653-4768-a918-00241d940a2b', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGOUT', 'SELORM logged out from AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-01 15:28:49', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('2c9055a9-b053-49f1-bf1a-74a046b8f502', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 15:47:14', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('2e4c3e60-e47c-4d67-848f-e870497b9461', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 18:08:31', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('35b2fb98-8743-4cf9-8832-9d78201aa212', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:44:18', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('36c91f79-526f-472e-9d0d-b3547304cbab', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:33:28', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('3762f4a8-7c98-4aa8-88f6-d684c0d61824', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 09:25:54', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('3a80c838-b0c8-41d1-8917-e6cf67e48e51', '97a5561d-13c5-4692-be66-5dddcc6819fc', 'JERRY JOHN', 'PENSIONS', 'LOGOUT', 'JERRY JOHN logged out from PENSIONS department', 'USER', '97a5561d-13c5-4692-be66-5dddcc6819fc', '{}', '2025-08-02 06:39:25', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('3c93466c-eb24-4fe7-9331-de5722bf8eba', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:46:05', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('3fcaaaee-be65-4717-85a7-a67c6ae81884', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 16:12:40', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('41c8217f-0f0c-44e1-9bba-511d6f8210f9', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 20:04:16', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('436df632-cedf-4300-8b2b-bea2db1ea96a', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 17:41:02', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('47cf3ed2-33b1-4fb2-9d7d-3ec62575d3e1', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 14:31:07', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('49082e63-7ed4-4d4e-a2dc-3d38fde6ce09', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:22:19', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('4b185bc5-c84f-45ea-94a6-f2e856c5ca5e', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 18:08:42', '10.239.197.104', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('4b2bed61-18e9-4d70-84bb-dd0b9db8af26', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 06:05:59', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('4d7625b1-ad70-41b7-91a0-96ff990b810f', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-01 15:23:49', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('5678ffd0-f943-4eac-afb1-7346835dc660', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 17:38:34', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('56de5d3f-2cda-4868-a23b-22a973548e1f', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 09:53:13', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('573284d0-8a6d-42be-86bc-c89aea980fff', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGOUT', 'SELORM logged out from AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 08:45:57', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('57985fe4-bd61-4c80-b5ea-25c5b27a82a1', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 18:08:14', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('5a99e6e0-3be3-4a72-a0b8-20cf95cfe338', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:35:52', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('5be8169c-e6ba-4636-8624-d1cb13ada4c7', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGOUT', 'SELORM logged out from AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 09:39:49', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('5ee7e995-2258-4904-8b31-abc823fe4d72', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 19:56:21', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('6077b763-4785-437a-a1ae-304ddc984f88', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', 'FINANCE', 'LOGIN', 'MR. FELIX AYISI logged in to FINANCE department', 'USER', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', '{}', '2025-08-02 06:39:35', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('6518f982-4bd1-41c7-869a-7caf44d228a3', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 08:43:16', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('65e72c06-4cfe-4444-99bc-f223a025eb62', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 14:26:48', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('6985d5f6-5bc8-4460-8513-e4c6cbc66ab5', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', 'FINANCE', 'LOGOUT', 'MR. FELIX AYISI logged out from FINANCE department', 'USER', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', '{}', '2025-08-02 06:39:53', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('6a522bf9-b158-4c1e-9e8a-2a8e2923e356', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:46:51', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('6a90f989-3812-45b6-baee-92ecebb98ebf', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 15:47:36', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('6f028de3-d635-4e27-9e78-edb68a479b97', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 09:35:35', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('70d356ef-ecae-4b63-b01c-fdc3ea45e547', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 10:12:02', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('75669716-ea35-4e5e-abad-2ef635fb156a', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 14:45:41', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('75a12a9f-7570-496e-a47a-57a5d86873a8', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 06:40:02', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('7ceda0e6-b63a-4dcb-bc3c-650075d497ba', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 15:53:22', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('7ebace76-b8e2-4420-bd50-fc8e0f15a061', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 12:34:51', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('8132015e-2a7c-4a8f-aaff-4ebe8d5f0002', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:59:27', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('81fca33b-7244-4d79-8a39-5cc9e33ea803', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', 'FINANCE', 'LOGOUT', 'MR. FELIX AYISI logged out from FINANCE department', 'USER', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', '{}', '2025-08-01 19:57:12', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('83065866-1016-48d7-9228-1ba75acdf9b7', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', 'FINANCE', 'LOGOUT', 'MR. FELIX AYISI logged out from FINANCE department', 'USER', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', '{}', '2025-08-02 06:54:37', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('87ed37dd-b93c-4f88-8397-848433e3b689', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:21:32', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('89616036-8b35-49fe-a340-b78ebe81c850', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 06:40:25', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('89846b6c-8cbb-43c4-a054-b9e32c831d8a', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 20:00:04', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('8a75af51-8215-4421-b560-35748854f1b0', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGOUT', 'SELORM logged out from AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 09:27:07', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('8ae78b16-a5f1-4b9c-8bca-44d4138dc47a', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 06:53:11', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('8b357d68-5d4d-49c2-bcd0-afe6d896d58b', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:22:09', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('8cb713ae-c61f-4df1-a554-f6cd27823c22', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:52:36', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('9266b84b-0b5f-43ec-ac3a-f2249d21894c', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 12:34:56', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('93332ef0-2acc-445d-ba4f-acf8b5d3bcbe', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', 'FINANCE', 'LOGIN', 'MR. FELIX AYISI logged in to FINANCE department', 'USER', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', '{}', '2025-08-02 05:44:59', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('961132de-d378-4737-a46b-d3c23c95bc0b', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:24:30', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('9653daa8-5eb2-4d63-bc13-7fc93466d91d', '24f05c6c-a7d4-49c6-a1b1-3649e5f473cb', 'GYAMPOH', 'PENTSOS', 'LOGIN', 'GYAMPOH logged in to PENTSOS department', 'USER', '24f05c6c-a7d4-49c6-a1b1-3649e5f473cb', '{}', '2025-08-02 07:17:22', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('9c2982f1-6011-4db8-9d9c-2c47489739f8', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 08:46:56', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('9d780475-7384-4ea5-9c9f-d81ba32f3cd0', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:29:35', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('a1dc1a30-989d-4f8f-a25e-19c7bdd33f91', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:01:55', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('a3682316-cb76-45a7-811a-e1c9586c7a85', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 20:03:38', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('a5d20aeb-0b0c-4a1f-9adf-9760de27b803', '97a5561d-13c5-4692-be66-5dddcc6819fc', 'JERRY JOHN', 'PENSIONS', 'LOGOUT', 'JERRY JOHN logged out from PENSIONS department', 'USER', '97a5561d-13c5-4692-be66-5dddcc6819fc', '{}', '2025-08-02 07:16:51', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('ab3b3cda-b23b-40d4-a905-04b5e85fe975', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', 'FINANCE', 'LOGIN', 'MR. FELIX AYISI logged in to FINANCE department', 'USER', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', '{}', '2025-08-02 08:23:20', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('ad671651-f340-49ff-89e1-042d51acd4c9', '97a5561d-13c5-4692-be66-5dddcc6819fc', 'JERRY JOHN', 'PENSIONS', 'LOGIN', 'JERRY JOHN logged in to PENSIONS department', 'USER', '97a5561d-13c5-4692-be66-5dddcc6819fc', '{}', '2025-08-02 06:23:43', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('ae9b13ca-ecd0-49c0-bcb4-34f4131a70a1', '24f05c6c-a7d4-49c6-a1b1-3649e5f473cb', 'GYAMPOH', 'PENTSOS', 'LOGOUT', 'GYAMPOH logged out from PENTSOS department', 'USER', '24f05c6c-a7d4-49c6-a1b1-3649e5f473cb', '{}', '2025-08-02 07:29:39', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('b31a20b7-33f4-4c37-9070-7a3480f66481', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:21:45', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('b4ebd83d-e5b8-4b1e-a5cc-04297f1378d3', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 13:29:07', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('b9d7847f-bd70-48f9-bea8-9888e76d1c59', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 15:19:07', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('c0862317-31c2-4f42-bb71-3d0d0da90712', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 14:31:40', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('c17713b2-f25b-44e7-aa79-25cfcda324cf', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 08:54:32', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('c1900a23-987b-4fe5-bf4b-936d98cd6526', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGOUT', 'SELORM logged out from AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-01 19:59:53', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('c255d135-b822-4379-ac6a-2140a1ef715a', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 09:44:31', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('ca1c5678-cc3d-4824-8887-72feec6bc650', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 19:57:20', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('ca3982ec-019a-4399-9d2e-ef1750bcc4bf', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 12:14:28', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('cb7184a0-4570-486a-a634-415d18dec54a', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-02 07:17:01', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('cd472293-1860-4f00-b4fc-92ccf2e97edc', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-02 07:17:11', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('cd8a4803-3326-47ac-9731-843a55e807e8', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 08:43:31', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('cdbd5279-fa09-432c-87de-111b1945a9ab', 'admin-default', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'LOGIN', 'SYSTEM ADMINISTRATOR logged in to SYSTEM ADMIN department', 'USER', 'admin-default', '{}', '2025-08-02 08:17:49', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('d1738404-fb8d-464d-833a-adc2d3e626a9', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:52:57', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('d2e71cab-9039-4f9f-8888-50121bd474ba', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 12:14:42', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('d40df321-f3fa-4cc7-bd74-9ab0fa12b3d4', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGOUT', 'SELORM logged out from AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 09:52:08', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('d68be7e1-076e-4cb9-9d43-cce4482d2a14', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 07:30:48', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('d770b49b-45d0-49b4-81b8-209ba358d140', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:29:34', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('dae301b5-92ed-4828-b010-bbdbd7a17442', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGOUT', 'SELORM logged out from AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 09:34:19', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('dd1cc0eb-56c0-43cf-b1f7-be4c3d90152d', '97a5561d-13c5-4692-be66-5dddcc6819fc', 'JERRY JOHN', 'PENSIONS', 'LOGIN', 'JERRY JOHN logged in to PENSIONS department', 'USER', '97a5561d-13c5-4692-be66-5dddcc6819fc', '{}', '2025-08-02 06:54:47', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('e01c08ab-714c-437f-9450-67b0ac7396cc', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', 'FINANCE', 'LOGIN', 'MR. FELIX AYISI logged in to FINANCE department', 'USER', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', '{}', '2025-08-01 19:56:34', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('eb5725e1-a739-45f5-8943-b346c8b3ffe3', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', 'FINANCE', 'LOGIN', 'MR. FELIX AYISI logged in to FINANCE department', 'USER', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', '{}', '2025-08-02 06:40:34', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('ed0bc242-6007-46a1-8588-f57d38ba21d1', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-01 19:40:03', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('ed43dd03-9fed-433a-ab6d-ae0c6013c87a', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 09:29:20', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('ef337844-ca7e-493c-82c8-46367ffd3aa9', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:17:36', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('ef7b4199-3288-465d-9d46-c4cd298f7bd1', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-02 09:58:07', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('f17a5888-a551-4bb0-9d84-8ea42e03b5d2', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-02 09:02:07', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('f806605f-2c4d-48f8-9620-f3a032ffb77c', 'admin-default', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'LOGOUT', 'SYSTEM ADMINISTRATOR logged out from SYSTEM ADMIN department', 'USER', 'admin-default', '{}', '2025-08-02 08:19:39', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('fbfc27c0-638f-4f2e-a1a7-d0ca197e7aaf', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 14:45:32', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO');

-- Table: audit_voucher_attachments
DROP TABLE IF EXISTS `audit_voucher_attachments`;
CREATE TABLE `audit_voucher_attachments` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `stored_filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `uploaded_by` varchar(36) NOT NULL,
  `uploaded_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_active` (`is_active`),
  KEY `idx_uploaded_at` (`uploaded_at`),
  CONSTRAINT `audit_voucher_attachments_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `audit_voucher_attachments_ibfk_2` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Stores metadata for audit department voucher attachments';

-- Data for table: audit_voucher_attachments
INSERT INTO `audit_voucher_attachments` VALUES
('2f76525b-a930-484e-bf29-14242213cd68', '3bc2c7b4-451c-40f5-b34f-d15a61fb61c7', 'Tumu-2025 Training grant budget  (1).pdf', 'OFORI_ATTA__BUILDING_EXP.pdf', 'C:\Users\<USER>\Desktop\VMS-PRODUCTION\uploads\audit-attachments\2025\voucher-3bc2c7b4-451c-40f5-b34f-d15a61fb61c7\OFORI_ATTA__BUILDING_EXP.pdf', 379457, 'application/pdf', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '2025-07-29 11:27:19', 1);

-- Table: batch_vouchers
DROP TABLE IF EXISTS `batch_vouchers`;
CREATE TABLE `batch_vouchers` (
  `batch_id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  PRIMARY KEY (`batch_id`,`voucher_id`),
  KEY `idx_batch_vouchers_batch_id` (`batch_id`),
  KEY `idx_batch_vouchers_voucher_id` (`voucher_id`),
  CONSTRAINT `batch_vouchers_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `voucher_batches` (`id`) ON DELETE CASCADE,
  CONSTRAINT `batch_vouchers_ibfk_2` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: batch_vouchers
INSERT INTO `batch_vouchers` VALUES
('0d2e2c8e-a858-4139-9692-b38011dbd776', 'b30466b6-6636-4b0a-9ee3-675b238134af'),
('2153618f-93dd-4132-aa5e-7cb00956f6f1', '5010d7f3-60b3-4921-8613-3ebe1ff99457'),
('4efdca48-eefe-4139-ba44-2c42ef51da28', 'b235c938-994e-4e70-88fc-ee685482d393'),
('5273d456-474f-451c-8eb3-a305401b3d26', 'e7f8f40a-91e5-41cf-92f8-2ac574cb6b34'),
('85d46515-8ccf-4da9-a48c-b1a0152846e4', 'b30466b6-6636-4b0a-9ee3-675b238134af'),
('860608f2-dcff-4fef-bb47-11ab1229c549', '6ec477c1-16ff-4a74-876c-ccfa14e0d1f4'),
('8c261ce1-0e71-4d84-b1f2-129bf3099f54', '9f34493f-d604-4f6d-8a0e-8edba3c5f85f'),
('b99d4c7f-1699-4b9f-a4cb-b44a2496e99e', 'b235c938-994e-4e70-88fc-ee685482d393'),
('c01ad37e-0d98-49d4-813e-6d840f30ba4c', 'b30466b6-6636-4b0a-9ee3-675b238134af'),
('cb50230d-6eb8-4db8-9dc8-87d4f3779983', '5ca8b4ff-9693-4f09-93c0-1fcb68fd21c4'),
('d875c754-6a30-4fb6-9983-1d9271cd0687', '43ef86df-816b-4c07-881e-5cea65b0bad5'),
('e65e9064-acf7-4b8d-ac09-517442f15aea', '3e1b728a-cab9-485d-b050-8df4a5e1445e'),
('f3b15dd3-d45a-44e7-af8e-ba77295462fd', 'b235c938-994e-4e70-88fc-ee685482d393'),
('fb69625a-ca9d-479d-9a33-f52993065a95', '966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2'),
('fd553b2e-769e-4942-bedd-e31f80a9d587', 'f622a8e4-0fc1-4719-b39c-e8bd162b840d');

-- Table: batches
DROP TABLE IF EXISTS `batches`;
CREATE TABLE `batches` (
  `id` varchar(36) NOT NULL,
  `batch_number` varchar(50) NOT NULL,
  `department` varchar(100) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(50) DEFAULT 'PENDING',
  `voucher_count` int DEFAULT '0',
  `total_amount` decimal(15,2) DEFAULT '0.00',
  `dispatch_date` timestamp NULL DEFAULT NULL,
  `received_date` timestamp NULL DEFAULT NULL,
  `received_by` varchar(36) DEFAULT NULL,
  `notes` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: batches
INSERT INTO `batches` VALUES
('BATCH_1753477346146', 'BATCH_1753477346146', 'AUDIT', 'SAMUEL', '2025-07-25 21:02:26', 'PENDING', 1, '1000.00', NULL, NULL, NULL, NULL);

-- Table: blacklisted_voucher_ids
DROP TABLE IF EXISTS `blacklisted_voucher_ids`;
CREATE TABLE `blacklisted_voucher_ids` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(50) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `voucher_id` (`voucher_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: concurrent_users_monitor
DROP TABLE IF EXISTS `concurrent_users_monitor`;
CREATE TABLE `concurrent_users_monitor` (
  `id` int NOT NULL AUTO_INCREMENT,
  `department` varchar(50) NOT NULL,
  `active_users_count` int DEFAULT '0',
  `max_users_limit` int DEFAULT '10',
  `workstations_count` int DEFAULT '0',
  `last_updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `peak_usage_time` time DEFAULT NULL,
  `peak_users_count` int DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_department` (`department`),
  KEY `idx_concurrent_department` (`department`),
  KEY `idx_concurrent_updated` (`last_updated`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: concurrent_users_monitor
INSERT INTO `concurrent_users_monitor` VALUES
(1, 'AUDIT', 0, 10, 3, '2025-05-29 20:26:56', NULL, 0),
(2, 'FINANCE', 0, 10, 3, '2025-05-29 20:26:56', NULL, 0),
(3, 'MINISTRIES', 0, 8, 2, '2025-05-29 20:26:56', NULL, 0),
(4, 'PENSIONS', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(5, 'PENTMEDIA', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(6, 'MISSIONS', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(7, 'PENTSOS', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(8, 'SYSTEM ADMIN', 0, 5, 1, '2025-05-29 20:26:56', NULL, 0);

-- Table: departments
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments` (
  `id` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(10) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: departments
INSERT INTO `departments` VALUES
('4', 'HR', '', 'Human Resources', 1, '2025-07-10 15:13:26', '2025-07-10 15:13:26'),
('dept_admin', 'SYSTEM ADMIN', 'ADM', 'System Administration', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_audit', 'AUDIT', 'AUD', 'Audit Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_finance', 'FINANCE', 'FIN', 'Finance Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_ministries', 'MINISTRIES', 'MIN', 'Ministries Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_missions', 'MISSIONS', 'MIS', 'Missions Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_pensions', 'PENSIONS', 'PEN', 'Pensions Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_pentmedia', 'PENTMEDIA', 'PMD', 'Pentmedia Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_pentsos', 'PENTSOS', 'PSO', 'Pentsos Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06');

-- Table: lan_resource_locks
DROP TABLE IF EXISTS `lan_resource_locks`;
CREATE TABLE `lan_resource_locks` (
  `id` varchar(36) NOT NULL,
  `resource_type` enum('VOUCHER','BATCH','CASH_RECORD','USER') NOT NULL,
  `resource_id` varchar(100) NOT NULL,
  `locked_by_user_id` varchar(36) NOT NULL,
  `locked_by_session_id` varchar(255) NOT NULL,
  `workstation_id` varchar(100) DEFAULT NULL,
  `lock_type` enum('read','write','exclusive') DEFAULT 'write',
  `locked_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_lan_locks_user` (`locked_by_user_id`),
  KEY `idx_lan_locks_session` (`locked_by_session_id`),
  KEY `idx_lan_locks_expires` (`expires_at`),
  KEY `idx_lan_locks_active` (`is_active`),
  KEY `idx_lan_locks_resource` (`resource_type`,`resource_id`),
  CONSTRAINT `lan_resource_locks_ibfk_1` FOREIGN KEY (`locked_by_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `lan_resource_locks_ibfk_2` FOREIGN KEY (`locked_by_session_id`) REFERENCES `lan_user_sessions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: lan_security_events
DROP TABLE IF EXISTS `lan_security_events`;
CREATE TABLE `lan_security_events` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) DEFAULT NULL,
  `event_type` enum('LOGIN_SUCCESS','LOGIN_FAILED','LOGOUT','PASSWORD_CHANGE','MFA_ENABLED','MFA_DISABLED','ACCOUNT_LOCKED','ACCOUNT_UNLOCKED','SUSPICIOUS_ACTIVITY') NOT NULL,
  `severity` enum('LOW','MEDIUM','HIGH','CRITICAL') DEFAULT 'MEDIUM',
  `description` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `workstation_id` varchar(100) DEFAULT NULL,
  `additional_data` json DEFAULT NULL,
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_lan_security_user_id` (`user_id`),
  KEY `idx_lan_security_type` (`event_type`),
  KEY `idx_lan_security_severity` (`severity`),
  KEY `idx_lan_security_timestamp` (`timestamp`),
  CONSTRAINT `lan_security_events_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: lan_user_sessions
DROP TABLE IF EXISTS `lan_user_sessions`;
CREATE TABLE `lan_user_sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `refresh_token` varchar(500) NOT NULL,
  `workstation_id` varchar(100) DEFAULT NULL,
  `workstation_name` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `mac_address` varchar(17) DEFAULT NULL,
  `user_agent` text,
  `department` varchar(50) DEFAULT NULL,
  `device_info` json DEFAULT NULL,
  `network_info` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL,
  `last_activity` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_heartbeat` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `session_type` enum('WORKSTATION','MOBILE','ADMIN') DEFAULT 'WORKSTATION',
  `concurrent_session_count` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_lan_sessions_user_id` (`user_id`),
  KEY `idx_lan_sessions_workstation` (`workstation_id`),
  KEY `idx_lan_sessions_department` (`department`),
  KEY `idx_lan_sessions_expires_at` (`expires_at`),
  KEY `idx_lan_sessions_active` (`is_active`),
  KEY `idx_lan_sessions_heartbeat` (`last_heartbeat`),
  CONSTRAINT `lan_user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: lan_workstations
DROP TABLE IF EXISTS `lan_workstations`;
CREATE TABLE `lan_workstations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `workstation_id` varchar(100) NOT NULL,
  `workstation_name` varchar(100) NOT NULL,
  `department` varchar(50) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `mac_address` varchar(17) DEFAULT NULL,
  `computer_name` varchar(100) DEFAULT NULL,
  `os_info` varchar(200) DEFAULT NULL,
  `last_seen` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `is_authorized` tinyint(1) DEFAULT '1',
  `max_concurrent_users` int DEFAULT '3',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workstation_id` (`workstation_id`),
  KEY `idx_workstations_department` (`department`),
  KEY `idx_workstations_active` (`is_active`),
  KEY `idx_workstations_ip` (`ip_address`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: lan_workstations
INSERT INTO `lan_workstations` VALUES
(1, 'AUDIT-WS-01', 'Audit Workstation 1', 'AUDIT', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(2, 'AUDIT-WS-02', 'Audit Workstation 2', 'AUDIT', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(3, 'AUDIT-WS-03', 'Audit Workstation 3', 'AUDIT', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(4, 'FINANCE-WS-01', 'Finance Workstation 1', 'FINANCE', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(5, 'FINANCE-WS-02', 'Finance Workstation 2', 'FINANCE', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(6, 'FINANCE-WS-03', 'Finance Workstation 3', 'FINANCE', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(7, 'MINISTRIES-WS-01', 'Ministries Workstation 1', 'MINISTRIES', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(8, 'MINISTRIES-WS-02', 'Ministries Workstation 2', 'MINISTRIES', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(9, 'PENSIONS-WS-01', 'Pensions Workstation 1', 'PENSIONS', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(10, 'PENTMEDIA-WS-01', 'Pentmedia Workstation 1', 'PENTMEDIA', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(11, 'MISSIONS-WS-01', 'Missions Workstation 1', 'MISSIONS', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(12, 'PENTSOS-WS-01', 'Pentsos Workstation 1', 'PENTSOS', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(13, 'ADMIN-WS-01', 'System Admin Workstation', 'SYSTEM ADMIN', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 5, '2025-05-29 20:26:56');

-- Table: migrations
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `applied_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_migration_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: notifications
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `timestamp` varchar(50) NOT NULL,
  `voucher_id` varchar(36) DEFAULT NULL,
  `batch_id` varchar(36) DEFAULT NULL,
  `type` varchar(50) NOT NULL,
  `from_audit` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_notifications_user_id` (`user_id`),
  KEY `idx_notifications_read` (`is_read`),
  KEY `idx_notifications_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: notifications
INSERT INTO `notifications` VALUES
('1a900d09-b8e6-492c-9d98-f565ff1295d2', 'FINANCE', 'Voucher FINAUG0003 certified by Audit', 0, '2025-08-02 06:06:04', 'e7f8f40a-91e5-41cf-92f8-2ac574cb6b34', NULL, 'VOUCHER_CERTIFIED', 0),
('1ef3c403-5335-492a-9b0c-17eac61245d3', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'New batch received from PENTSOS', 1, '2025-08-02 07:19:23', NULL, '4efdca48-eefe-4139-ba44-2c42ef51da28', 'NEW_BATCH', 0),
('1f6d3c63-a0b7-4267-be3a-8a3723279d72', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'New batch received from PENTSOS', 1, '2025-08-02 07:27:47', NULL, 'b99d4c7f-1699-4b9f-a4cb-b44a2496e99e', 'NEW_BATCH', 0),
('2d25d063-d074-4efa-a466-5402d9360b40', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'New batch received from PENTSOS', 1, '2025-08-02 07:19:23', NULL, '4efdca48-eefe-4139-ba44-2c42ef51da28', 'NEW_BATCH', 0),
('2d64c9e8-368c-4cf6-832b-6de3691336a7', 'AUDIT', 'Voucher PSOAUG0003 received from Audit', 0, '2025-08-02 07:21:16', 'b235c938-994e-4e70-88fc-ee685482d393', NULL, 'VOUCHER_RECEIVED', 0),
('34e6e89c-7352-4a6b-8892-ef92f4b75ddd', 'MISSIONS', 'Voucher MISAUG0001 certified by Audit', 0, '2025-08-01 18:09:29', 'f622a8e4-0fc1-4719-b39c-e8bd162b840d', NULL, 'VOUCHER_CERTIFIED', 0),
('60e10820-823a-48d1-b5e0-eb5d1aa66c8a', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'New batch received from MISSIONS', 1, '2025-08-01 18:09:23', NULL, 'fd553b2e-769e-4942-bedd-e31f80a9d587', 'NEW_BATCH', 0),
('67cd3354-a5c1-4b93-8675-67a673418282', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'New batch received from MISSIONS', 1, '2025-08-01 20:01:03', NULL, 'd875c754-6a30-4fb6-9983-1d9271cd0687', 'NEW_BATCH', 0),
('820697d0-4030-43c2-8158-535f0dcd594b', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'New batch received from MISSIONS', 1, '2025-08-01 18:09:23', NULL, 'fd553b2e-769e-4942-bedd-e31f80a9d587', 'NEW_BATCH', 0),
('8910a9a8-5570-4f6b-bdfd-5abec4b898d3', 'MISSIONS', 'Voucher MISAUG0002 certified by Audit', 0, '2025-08-01 20:01:34', '43ef86df-816b-4c07-881e-5cea65b0bad5', NULL, 'VOUCHER_CERTIFIED', 0),
('9cd27952-b80a-49aa-8a8c-4cfd1a5d11f5', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'New batch received from FINANCE', 1, '2025-08-02 05:50:47', NULL, '5273d456-474f-451c-8eb3-a305401b3d26', 'NEW_BATCH', 0),
('a005337d-7cca-4c25-96a9-65525aec14c8', 'PENTSOS', 'Voucher PSOAUG0003 certified by Audit', 0, '2025-08-02 07:19:36', 'b235c938-994e-4e70-88fc-ee685482d393', NULL, 'VOUCHER_CERTIFIED', 0),
('b58ed8b2-b7d2-4137-9197-5aed2f6f9d75', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'New batch received from FINANCE', 1, '2025-08-02 05:50:47', NULL, '5273d456-474f-451c-8eb3-a305401b3d26', 'NEW_BATCH', 0),
('c34edda6-2ee6-424a-91c6-b3e73bed7066', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'New batch received from MISSIONS', 1, '2025-08-01 20:01:03', NULL, 'd875c754-6a30-4fb6-9983-1d9271cd0687', 'NEW_BATCH', 0),
('dc6673ec-e395-44c2-ab8d-978068b9b501', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'New batch received from PENTSOS', 1, '2025-08-02 07:27:47', NULL, 'b99d4c7f-1699-4b9f-a4cb-b44a2496e99e', 'NEW_BATCH', 0),
('eccc0996-903b-49f9-8d60-50940e53da66', 'PENTSOS', 'Voucher PSOAUG0003 certified by Audit', 0, '2025-08-02 07:28:02', 'b235c938-994e-4e70-88fc-ee685482d393', NULL, 'VOUCHER_CERTIFIED', 0);

-- Table: password_change_requests
DROP TABLE IF EXISTS `password_change_requests`;
CREATE TABLE `password_change_requests` (
  `id` varchar(50) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `user_name` varchar(100) NOT NULL,
  `user_department` varchar(50) NOT NULL,
  `new_password_hash` text NOT NULL,
  `status` enum('PENDING','APPROVED','REJECTED') DEFAULT 'PENDING',
  `requested_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `requested_by_ip` varchar(45) DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `processed_by` varchar(100) DEFAULT NULL,
  `admin_notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_requested_at` (`requested_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: password_change_requests
INSERT INTO `password_change_requests` VALUES
('PWD_REQ_1753545774953', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'newpass123', 'REJECTED', '2025-07-26 16:02:54', '::1', '2025-07-26 16:12:15', 'ADMIN', 'Rejected by administrator', '2025-07-26 16:02:54', '2025-07-26 16:12:15'),
('PWD_REQ_1753547895718', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'newpassword456', 'REJECTED', '2025-07-26 16:38:15', '::1', '2025-07-26 16:40:33', 'ADMIN', 'Rejected by administrator', '2025-07-26 16:38:15', '2025-07-26 16:40:33'),
('PWD_REQ_1753547959991', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'newpassword456', 'REJECTED', '2025-07-26 16:39:20', '::1', '2025-07-26 16:40:30', 'ADMIN', 'Rejected by administrator', '2025-07-26 16:39:20', '2025-07-26 16:40:30'),
('PWD_REQ_1753548078067', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'james123', 'APPROVED', '2025-07-26 16:41:18', '::1', '2025-07-26 16:50:05', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-26 16:41:18', '2025-07-26 16:50:05'),
('PWD_REQ_1753548719134', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'james123', 'APPROVED', '2025-07-26 16:51:59', '::1', '2025-07-26 16:52:32', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-26 16:51:59', '2025-07-26 16:52:32'),
('PWD_REQ_1753712987545', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', '123123', 'APPROVED', '2025-07-28 14:29:47', '10.25.42.61', '2025-07-28 14:31:34', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-28 14:29:47', '2025-07-28 14:31:34'),
('PWD_REQ_1753713012221', 'abe28dd4-00f4-4d23-80f6-246235a79c51', 'SAMMY MAWUKO', 'MINISTRIES', '123123', 'APPROVED', '2025-07-28 14:30:12', '10.25.42.61', '2025-07-28 14:31:32', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-28 14:30:12', '2025-07-28 14:31:32'),
('PWD_REQ_1753713049142', '97a5561d-13c5-4692-be66-5dddcc6819fc', 'JERRY JOHN', 'PENSIONS', '123123', 'APPROVED', '2025-07-28 14:30:49', '10.25.42.61', '2025-07-28 14:31:30', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-28 14:30:49', '2025-07-28 14:31:30'),
('PWD_REQ_1753713078682', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', 'FINANCE', '123123', 'APPROVED', '2025-07-28 14:31:18', '10.25.42.61', '2025-07-28 14:31:27', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-28 14:31:18', '2025-07-28 14:31:27'),
('PWD_REQ_1753802611249', '8627ce90-b753-4845-a467-7475fc7cf994', 'JAMES ', 'MINISTRIES', '123123', 'APPROVED', '2025-07-29 15:23:31', '10.25.41.192', '2025-07-29 15:23:40', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-29 15:23:31', '2025-07-29 15:23:40'),
('PWD_REQ_1753802753619', '8627ce90-b753-4845-a467-7475fc7cf994', 'JAMES ', 'MINISTRIES', '123456', 'APPROVED', '2025-07-29 15:25:53', '10.25.42.61', '2025-07-29 15:26:31', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-29 15:25:53', '2025-07-29 15:26:31');

-- Table: pending_registrations
DROP TABLE IF EXISTS `pending_registrations`;
CREATE TABLE `pending_registrations` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `department` varchar(50) NOT NULL,
  `date_requested` varchar(50) NOT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: pending_registrations
INSERT INTO `pending_registrations` VALUES
('120a94dc-b6e0-4e05-8636-2eaff2937f68', 'SAMMY MAWUKO', '123456789', 'MINISTRIES', '2025-07-28 11:24:13', 'approved'),
('313ecfce-ef95-4458-8256-0b01699e5bc2', 'TEST USER REALTIME', 'testpass123', 'FINANCE', '2025-07-28 11:06:15', 'rejected'),
('3343ae67-03cc-484c-a0d1-7289e02e26a1', 'WILLIAM AKUAMOAH', '123', 'AUDIT', '2025-07-16 09:15:55', 'approved'),
('396fcb4e-c87f-4143-8a76-2201f83e4a6b', 'JAMES ARTHUR', '123123', 'PENTMEDIA', '2025-07-30 20:06:02', 'approved'),
('3ec42744-9fe5-4327-9f6b-bc4fd3b41567', 'JAMES ', '123123', 'MINISTRIES', '2025-07-29 15:21:59', 'approved'),
('47ea70bf-54c3-43ec-a456-7a88e8efc98b', 'JERRY JOHN', '123', 'PENSIONS', '2025-07-28 07:29:57', 'approved'),
('578933af-2c54-4fab-a312-ef5861ed1ebd', 'GYAMPOH', '123123', 'PENTSOS', '2025-07-30 20:07:35', 'approved'),
('6b3e9871-aecf-4069-a71c-38097afaf33b', 'JERRY JOHN', '123', 'PENSIONS', '2025-07-27 22:05:42', 'approved'),
('88e0b4b8-bb89-45e8-a66e-d24b37d70690', 'JAMES NABEL', '123', 'FINANCE', '2025-07-16 09:13:59', 'approved'),
('a230930c-1d91-472f-a90a-36c4558986d5', 'CHARIS', '123123', 'MISSIONS', '2025-07-30 20:06:35', 'approved'),
('a2b6ca0b-225f-4122-ac97-88e69a3a4208', 'EMMANUEL AMOAKOH', '123456789', 'AUDIT', '2025-07-28 10:59:07', 'approved'),
('c53c5b75-fe20-4578-a946-4133191eb4dc', 'MR. FELIX AYISI', '123456789', 'FINANCE', '2025-07-28 11:25:52', 'approved'),
('dfd81ba0-8ff1-4cfc-b98b-dac6e647853a', 'REALTIME TEST USER', 'testpass123', 'FINANCE', '2025-07-28 11:20:54', 'rejected'),
('e3d422ce-460f-428a-9f9a-7254cfe2ebe9', 'SELORM', '123123', 'AUDIT', '2025-07-29 13:29:16', 'approved'),
('f2b8ff4e-56b7-443c-9d22-76196fc45aa5', 'JJ', '123456789', 'PENSIONS', '2025-07-28 11:08:45', 'approved');

-- Table: permissions
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `display_name` varchar(150) NOT NULL,
  `description` text,
  `resource` varchar(50) NOT NULL,
  `action` varchar(50) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: permissions
INSERT INTO `permissions` VALUES
(1, 'system.admin', 'System Administration', 'Full system administration access', 'system', 'admin', '2025-05-29 20:26:54'),
(2, 'system.config', 'System Configuration', 'Configure system settings', 'system', 'config', '2025-05-29 20:26:54'),
(3, 'users.create', 'Create Users', 'Create new user accounts', 'users', 'create', '2025-05-29 20:26:54'),
(4, 'users.view', 'View Users', 'View user information', 'users', 'view', '2025-05-29 20:26:54'),
(5, 'users.edit', 'Edit Users', 'Edit user information', 'users', 'edit', '2025-05-29 20:26:54'),
(6, 'users.delete', 'Delete Users', 'Delete user accounts', 'users', 'delete', '2025-05-29 20:26:54'),
(7, 'users.manage_roles', 'Manage User Roles', 'Assign and modify user roles', 'users', 'manage_roles', '2025-05-29 20:26:54'),
(8, 'audit.vouchers.view', 'View Audit Vouchers', 'View vouchers in audit', 'audit.vouchers', 'view', '2025-05-29 20:26:54'),
(9, 'audit.vouchers.process', 'Process Audit Vouchers', 'Process and approve vouchers', 'audit.vouchers', 'process', '2025-05-29 20:26:54'),
(10, 'audit.vouchers.reject', 'Reject Audit Vouchers', 'Reject vouchers with comments', 'audit.vouchers', 'reject', '2025-05-29 20:26:54'),
(11, 'audit.batches.receive', 'Receive Voucher Batches', 'Receive voucher batches from departments', 'audit.batches', 'receive', '2025-05-29 20:26:54'),
(12, 'audit.batches.dispatch', 'Dispatch Voucher Batches', 'Send vouchers back to departments', 'audit.batches', 'dispatch', '2025-05-29 20:26:54'),
(13, 'department.vouchers.create', 'Create Department Vouchers', 'Create new vouchers', 'department.vouchers', 'create', '2025-05-29 20:26:54'),
(14, 'department.vouchers.view', 'View Department Vouchers', 'View department vouchers', 'department.vouchers', 'view', '2025-05-29 20:26:54'),
(15, 'department.vouchers.edit', 'Edit Department Vouchers', 'Edit voucher information', 'department.vouchers', 'edit', '2025-05-29 20:26:54'),
(16, 'department.vouchers.send', 'Send Vouchers to Audit', 'Send vouchers to audit department', 'department.vouchers', 'send', '2025-05-29 20:26:54'),
(17, 'vouchers.create', 'Create Vouchers', 'Create new vouchers', 'vouchers', 'create', '2025-05-29 20:26:54'),
(18, 'vouchers.view', 'View Vouchers', 'View voucher information', 'vouchers', 'view', '2025-05-29 20:26:54'),
(19, 'vouchers.edit', 'Edit Vouchers', 'Edit voucher information', 'vouchers', 'edit', '2025-05-29 20:26:54'),
(20, 'vouchers.delete', 'Delete Vouchers', 'Delete vouchers', 'vouchers', 'delete', '2025-05-29 20:26:54'),
(21, 'reports.view', 'View Reports', 'View system reports', 'reports', 'view', '2025-05-29 20:26:54'),
(22, 'reports.create', 'Create Reports', 'Create custom reports', 'reports', 'create', '2025-05-29 20:26:54'),
(23, 'reports.export', 'Export Reports', 'Export reports to various formats', 'reports', 'export', '2025-05-29 20:26:54');

-- Table: provisional_cash_records
DROP TABLE IF EXISTS `provisional_cash_records`;
CREATE TABLE `provisional_cash_records` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `voucher_ref` varchar(50) NOT NULL,
  `claimant` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `main_amount` decimal(15,2) NOT NULL,
  `currency` enum('GHS','USD','GBP','EUR','CFA') NOT NULL,
  `amount_retired` decimal(15,2) DEFAULT NULL,
  `clearance_remark` enum('CLEARED','REFUNDED TO CHEST','DUE STAFF','RETURNED') DEFAULT NULL,
  `date_retired` varchar(50) DEFAULT NULL,
  `cleared_by` varchar(255) DEFAULT NULL,
  `comment` text,
  `date` varchar(50) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_provisional_cash_voucher_id` (`voucher_id`),
  KEY `idx_provisional_cash_date` (`date`),
  CONSTRAINT `provisional_cash_records_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: provisional_cash_records
INSERT INTO `provisional_cash_records` VALUES
('202198b8-f6e3-4855-b9e6-d7a442eb7335', 'b15ebfe1-6b2c-4cba-9ef4-60bd18b20a57', 'FINJUL0004', 'louis', 'test', '9000.00', 'GHS', NULL, NULL, NULL, NULL, NULL, '2025-07-28_14-09-31'),
('3318cc72-e34e-49e9-873e-7bacc3dacf74', '18e93e0c-d1c5-44e5-a753-fed07cd2d120', 'FINJUL0005', 'TOM', 'TESTER', '7800.00', 'GHS', NULL, NULL, NULL, NULL, NULL, '2025-07-28_15-22-12'),
('4f5bb8f3-defd-4096-9756-6f4ccd591e55', 'bd459552-c902-453b-9ce4-45bcd1404aec', 'FINJUL0002', 'YAW MENSAH', 'OVER TIME ', '15400.00', 'GHS', '1800.00', 'REFUNDED TO CHEST', '29-JUL-2025 12:50:21 PM', 'EMMANUEL AMOAKOH', '', '2025-07-29_10-46-59'),
('531d83ec-838b-4534-bd4c-85a3fc9b4545', '504e8a6d-e2f6-414e-80d3-d59fd5897619', 'FINJUL0002', 'KOOO NIMO', 'AIR TICKET ', '2500.00', 'USD', '9000.00', 'DUE STAFF', '29-JUL-2025 09:32:38 PM', 'EMMANUEL AMOAKOH', '', '2025-07-29_15-05-49'),
('55d128a0-214e-462e-bf3e-6d6a023263e2', '172f145a-9b9f-409a-be3f-8bcbd78bb0b3', 'FINJUL0001', 'KOFI YESU', 'MEDICAL BILLS ', '70000.00', 'GHS', NULL, NULL, NULL, NULL, NULL, '2025-07-29_10-07-00'),
('64cfb422-6fce-4ed0-8885-4c2d7491a38b', '43ef86df-816b-4c07-881e-5cea65b0bad5', 'MISAUG0002', 'JOSH', 'TEST', '900000.00', 'CFA', NULL, NULL, NULL, NULL, NULL, '2025-08-01_20-02-40'),
('f335ca07-313a-4b76-91ef-082bcd59bc21', 'ec7b5f4e-d7f0-4f3a-9210-f6070aa3e817', 'FINJUL0008', 'OFORI ATTA ', 'WOMEN LACOST ', '47000.00', 'GHS', NULL, NULL, NULL, NULL, NULL, '2025-07-29_11-35-22');

-- Table: resource_locks
DROP TABLE IF EXISTS `resource_locks`;
CREATE TABLE `resource_locks` (
  `id` varchar(36) NOT NULL,
  `resource_type` varchar(50) NOT NULL,
  `resource_id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `department` varchar(50) NOT NULL,
  `lock_time` datetime NOT NULL,
  `expiry_time` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_resource` (`resource_type`,`resource_id`),
  KEY `idx_resource_locks_type_id` (`resource_type`,`resource_id`),
  KEY `idx_resource_locks_user` (`user_id`),
  KEY `idx_resource_locks_expiry` (`expiry_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: role_permissions
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` int NOT NULL,
  `permission_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_role_permission` (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: roles
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `description` text,
  `permissions` json DEFAULT NULL,
  `is_system_role` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: roles
INSERT INTO `roles` VALUES
(1, 'SUPER_ADMIN', 'Super Administrator', 'Full system access with all permissions', *, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(2, 'AUDIT_MANAGER', 'Audit Manager', 'Full audit department management access', audit.*,users.view,reports.*, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(3, 'AUDIT_USER', 'Audit User', 'Standard audit user with voucher processing access', audit.vouchers.*,audit.batches.*,reports.view, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(4, 'DEPT_MANAGER', 'Department Manager', 'Department management access', department.*,vouchers.*,users.view, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(5, 'DEPT_USER', 'Department User', 'Standard department user access', vouchers.create,vouchers.view,vouchers.edit, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(6, 'VIEWER', 'Viewer', 'Read-only access to assigned resources', vouchers.view,reports.view, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54');

-- Table: system_settings
DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fiscal_year_start` enum('JAN','FEB','MAR','APR','MAY','JUN','JUL','AUG','SEP','OCT','NOV','DEC') DEFAULT 'JAN',
  `fiscal_year_end` enum('JAN','FEB','MAR','APR','MAY','JUN','JUL','AUG','SEP','OCT','NOV','DEC') DEFAULT 'DEC',
  `current_fiscal_year` int NOT NULL,
  `system_time` varchar(50) NOT NULL,
  `auto_backup_enabled` tinyint(1) DEFAULT '1',
  `session_timeout` int DEFAULT '30',
  `last_backup_date` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: system_settings
INSERT INTO `system_settings` VALUES
(1, 'JAN', 'DEC', 2025, '2025-07-12 19:48:10', 1, 30, '2025-08-02 10:02:09'),
(2, 'JAN', 'DEC', 2025, '2025-06-19 14:21:15', 1, 30, NULL);

-- Table: users
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','USER','viewer') NOT NULL,
  `department` varchar(50) NOT NULL,
  `date_created` datetime NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `email` varchar(255) DEFAULT NULL,
  `last_selected_year` int DEFAULT '2025',
  PRIMARY KEY (`id`),
  KEY `idx_users_department` (`department`),
  KEY `idx_users_role` (`role`),
  KEY `idx_users_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: users
INSERT INTO `users` VALUES
('0c3f28c0-a2a1-472f-a601-0e3a0f054d13', 'JAMES ARTHUR', '123123', 'USER', 'PENTMEDIA', '2025-07-30 20:08:06', '2025-07-31 12:36:08', 1, NULL, 2025),
('24f05c6c-a7d4-49c6-a1b1-3649e5f473cb', 'GYAMPOH', '123123', 'USER', 'PENTSOS', '2025-07-30 20:08:09', '2025-08-02 07:17:22', 1, NULL, 2025),
('30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', '123123', 'USER', 'FINANCE', '2025-07-28 11:27:08', '2025-08-02 08:23:20', 1, NULL, 2025),
('7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', '123123', 'USER', 'MISSIONS', '2025-07-30 20:08:12', '2025-08-02 07:17:01', 1, NULL, 2025),
('8627ce90-b753-4845-a467-7475fc7cf994', 'JAMES ', '123456', 'USER', 'MINISTRIES', '2025-07-29 15:22:14', '2025-07-29 15:38:05', 1, NULL, 2025),
('8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', '123123', 'USER', 'AUDIT', '2025-07-29 13:29:27', '2025-08-02 10:11:51', 1, NULL, 2025),
('97a5561d-13c5-4692-be66-5dddcc6819fc', 'JERRY JOHN', '123123', 'USER', 'PENSIONS', '2025-07-28 08:32:38', '2025-08-02 06:54:47', 1, NULL, 2025),
('abe28dd4-00f4-4d23-80f6-246235a79c51', 'SAMMY MAWUKO', '123123', 'USER', 'MINISTRIES', '2025-07-28 11:24:27', '2025-07-30 21:11:46', 1, NULL, 2025),
('admin-default', 'SYSTEM ADMINISTRATOR', 'enter123', 'admin', 'SYSTEM ADMIN', '2025-06-19 14:21:15', '2025-08-02 08:17:49', 1, NULL, 2025),
('d97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', '123123', 'USER', 'AUDIT', '2025-07-28 10:59:21', '2025-08-02 10:12:02', 1, NULL, 2025);

-- Table: voucher_audit_log
DROP TABLE IF EXISTS `voucher_audit_log`;
CREATE TABLE `voucher_audit_log` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `voucher_id` varchar(50) NOT NULL,
  `action` varchar(100) NOT NULL,
  `performed_by` varchar(255) NOT NULL,
  `department` varchar(50) NOT NULL,
  `audit_reason` text,
  `changes_json` text,
  `original_values_json` text,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_performed_by` (`performed_by`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: voucher_audit_log
INSERT INTO `voucher_audit_log` VALUES
('2d7cbf3f-694e-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'TAX WRONGLY CALCULATED', '{"claimant":"DAN","description":"TEST","amount":"4500.00","preAuditedAmount":"5000.00","taxType":"GOODS 3%","taxAmount":250,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"DAN","description":"TEST","amount":"4500.00","preAuditedAmount":"5000.00","taxType":"SERVICE 7.5%","taxAmount":"600.00","preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-25 11:54:53'),
('4d12766e-6c6b-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'EMMANUEL AMOAKOH', 'AUDIT', 'at managers request', '{"claimant":"KOFI YESU","description":"MEDICAL BILLS ","amount":"70000.00","preAuditedAmount":15000,"taxType":"GOODS 3%","taxAmount":6000,"preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"USD"}', '{"claimant":"KOFI YESU","description":"MEDICAL BILLS ","amount":"70000.00","preAuditedAmount":"8000.00","taxType":"SERVICE 7.5%","taxAmount":"800.00","preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"GHS"}', '2025-07-29 11:00:55'),
('5f532392-6b00-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'TRYING THIS OUT', '{"claimant":"DUAH","description":"TEST","amount":"5600.00","preAuditedAmount":9000,"taxType":"GOODS 3%","taxAmount":900,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"DUAH","description":"TEST","amount":"5600.00","preAuditedAmount":"8000.00","taxType":"SERVICE 7.5%","taxAmount":"250.00","preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-27 15:42:59'),
('96aaa013-6c6a-11f0-8409-0a0027000013', 'FINJUL0002', 'POST_TRANSACTION_EDIT', 'EMMANUEL AMOAKOH', 'AUDIT', 'manager requested we do these chnages', '{"claimant":"YAW MENSAH","description":"MEDICALS","amount":"15400.00","preAuditedAmount":74999.99,"taxType":"SERVICE 7.5%","taxAmount":0,"preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"EUR"}', '{"claimant":"YAW MENSAH","description":"OVER TIME ","amount":"15400.00","preAuditedAmount":"16000.00","taxType":"SERVICE 7.5%","taxAmount":null,"preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"GHS"}', '2025-07-29 10:55:49'),
('a5cefe94-6a7f-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'JUST TRYING', '{"claimant":"TAN","description":"TEST","amount":"4500.00","preAuditedAmount":9500,"taxType":"SERVICE 7.5%","taxAmount":250,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"TAN","description":"test","amount":"4500.00","preAuditedAmount":"8000.00","taxType":null,"taxAmount":null,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-27 00:21:32'),
('b50df61f-6b37-11f0-8409-0a0027000013', 'PENJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'TELLING IT ALL TO THEM', '{"claimant":"JERRY","description":"TEST","amount":"8900.00","preAuditedAmount":15000,"taxType":"SERVICE 7.5%","taxAmount":1500,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"JERRY","description":"TEST","amount":"8900.00","preAuditedAmount":"90000.00","taxType":null,"taxAmount":null,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-27 22:19:05'),
('b8baf5ec-6a80-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'AM TRYING THIS OUT FOR TESTING', '{"claimant":"TAN","description":"TEST","amount":"4500.00","preAuditedAmount":15000,"taxType":"SERVICE 7.5%","taxAmount":500,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"TAN","description":"TEST","amount":"4500.00","preAuditedAmount":"9500.00","taxType":"SERVICE 7.5%","taxAmount":"250.00","preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-27 00:29:13'),
('c8554283-6e0b-11f0-af6f-a8b13b1317f2', 'PSOJUL0001', 'POST_TRANSACTION_EDIT', 'EMMANUEL AMOAKOH', 'AUDIT', 'UNDER FADS INSTRUCTIONS', '{"claimant":"GYAMPOH","description":"PURCHASES","amount":"9000.00","preAuditedAmount":800000,"taxType":"GOODS 3%","taxAmount":1000,"preAuditedBy":"SELORM","certifiedBy":"EMMANUEL AMOAKOH","currency":"CFA"}', '{"claimant":"GYAMPOH","description":"TEST","amount":"9000.00","preAuditedAmount":"700000.00","taxType":"SERVICE 7.5%","taxAmount":"900.00","preAuditedBy":"SELORM","certifiedBy":"EMMANUEL AMOAKOH","currency":"GHS"}', '2025-07-31 12:42:13'),
('d4b07245-6c6a-11f0-8409-0a0027000013', 'FINJUL0002', 'POST_TRANSACTION_EDIT', 'EMMANUEL AMOAKOH', 'AUDIT', 'AT MANAGERS REQUEST', '{"claimant":"YAW MENSAH","description":"MEDICALS","amount":"15400.00","preAuditedAmount":96000,"taxType":"GOODS 3%","taxAmount":500,"preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"EUR"}', '{"claimant":"YAW MENSAH","description":"MEDICALS","amount":"15400.00","preAuditedAmount":"74999.99","taxType":"SERVICE 7.5%","taxAmount":"0.00","preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"EUR"}', '2025-07-29 10:57:33'),
('f87a2892-694d-11f0-8409-0a0027000013', 'TEST-POST-EDIT-001', 'POST_TRANSACTION_EDIT', 'WILLIAM AKUAMOAH', 'AUDIT', 'Test post-transaction edit', '{"amount":1200,"description":"Updated description via post-transaction edit","claimant":"Updated Claimant Name"}', '{"amount":"1000.00","description":"Test voucher for post-transaction edit","claimant":"Test Claimant"}', '2025-07-25 11:53:25');

-- Table: voucher_batches
DROP TABLE IF EXISTS `voucher_batches`;
CREATE TABLE `voucher_batches` (
  `id` varchar(36) NOT NULL,
  `department` varchar(50) NOT NULL,
  `sent_by` varchar(255) NOT NULL,
  `sent_time` varchar(50) NOT NULL,
  `received` tinyint(1) DEFAULT '0',
  `from_audit` tinyint(1) DEFAULT '0',
  `voucher_ids` text,
  `contains_rejected_vouchers` tinyint(1) DEFAULT '0',
  `rejected_voucher_count` int DEFAULT '0',
  `contains_rejected_copies` tinyint(1) DEFAULT '0',
  `rejected_copy_count` int DEFAULT '0',
  `normal_voucher_count` int DEFAULT '0',
  `contains_resubmissions` tinyint(1) DEFAULT '0',
  `resubmission_count` int DEFAULT '0',
  `contains_returned_vouchers` tinyint(1) DEFAULT '0',
  `returned_voucher_count` int DEFAULT '0',
  `received_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_voucher_batches_department` (`department`),
  KEY `idx_voucher_batches_received` (`received`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: voucher_batches
INSERT INTO `voucher_batches` VALUES
('4efdca48-eefe-4139-ba44-2c42ef51da28', 'PENTSOS', 'GYAMPOH', '2025-08-02 07:19:23', 1, 0, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'EMMANUEL AMOAKOH'),
('5273d456-474f-451c-8eb3-a305401b3d26', 'FINANCE', 'MR. FELIX AYISI', '2025-08-02 05:50:47', 1, 0, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'EMMANUEL AMOAKOH'),
('b99d4c7f-1699-4b9f-a4cb-b44a2496e99e', 'PENTSOS', 'GYAMPOH', '2025-08-02 07:27:47', 1, 0, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'EMMANUEL AMOAKOH'),
('d875c754-6a30-4fb6-9983-1d9271cd0687', 'MISSIONS', 'CHARIS', '2025-08-01 20:01:03', 1, 0, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'EMMANUEL AMOAKOH'),
('f3b15dd3-d45a-44e7-af8e-ba77295462fd', 'PENTSOS', 'EMMANUEL AMOAKOH', '2025-08-02 07:20:51', 1, 1, NULL, 0, 0, 0, 0, 0, 0, 0, 1, 1, 'GYAMPOH'),
('fd553b2e-769e-4942-bedd-e31f80a9d587', 'MISSIONS', 'CHARIS', '2025-08-01 18:09:23', 1, 0, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'EMMANUEL AMOAKOH');

-- Table: voucher_complete_history
DROP TABLE IF EXISTS `voucher_complete_history`;
undefined;

-- Data for table: voucher_complete_history
INSERT INTO `voucher_complete_history` VALUES
('PENAUG0008', 'VENESSA', 'PENDING SUBMISSION', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('FINAUG0005', 'GETTY', 'PENDING SUBMISSION', 'FINANCE', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('PENAUG0006', 'LARRY', 'PENDING SUBMISSION', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('MISAUG0002', 'JOSH', 'AUDIT: PROCESSING', 'AUDIT', 0, 1, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('FINAUG0002', 'CLARK KENT', 'PENDING SUBMISSION', 'FINANCE', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('PSOAUG0003-RETURN-COPY', 'KENPONG', 'VOUCHER RETURNED', 'AUDIT', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('PSOAUG0001', 'CLARK KENT', 'PENDING SUBMISSION', 'PENTSOS', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('PSOAUG0003', 'KENPONG SAM', 'AUDIT: PROCESSING', 'AUDIT', 0, 0, 0, NULL, 'INSUFFICIENT ATTACHMENT', NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('PSOAUG0003-PENTSOS-RETURN-COPY', 'KENPONG', 'VOUCHER RETURNED', 'PENTSOS', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('PENAUG0007', 'KENPONG', 'PENDING SUBMISSION', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('FINAUG0001', 'JAMES', 'PENDING SUBMISSION', 'FINANCE', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('FINAUG0003', 'fairy tale', 'AUDIT: PROCESSING', 'AUDIT', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('PSOAUG0002', 'KWAME', 'PENDING SUBMISSION', 'PENTSOS', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('FINAUG0004', 'NAT', 'PENDING SUBMISSION', 'FINANCE', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('PENAUG0009', 'KOJO', 'PENDING SUBMISSION', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('MISAUG0001', 'MAX CHIDI', 'AUDIT: PROCESSING', 'AUDIT', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0);

-- Table: voucher_corrections
DROP TABLE IF EXISTS `voucher_corrections`;
CREATE TABLE `voucher_corrections` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `voucher_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `corrected_by` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `correction_time` datetime NOT NULL,
  `field_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `old_value` text COLLATE utf8mb4_unicode_ci,
  `new_value` text COLLATE utf8mb4_unicode_ci,
  `reason` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_corrected_by` (`corrected_by`),
  KEY `idx_correction_time` (`correction_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: voucher_department_visibility
DROP TABLE IF EXISTS `voucher_department_visibility`;
CREATE TABLE `voucher_department_visibility` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `department` varchar(50) NOT NULL,
  `visibility_reason` varchar(50) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_voucher_dept` (`voucher_id`,`department`),
  KEY `idx_voucher_department_visibility_voucher_id` (`voucher_id`),
  KEY `idx_voucher_department_visibility_department` (`department`),
  KEY `idx_voucher_department_visibility_created_at` (`created_at`),
  CONSTRAINT `voucher_department_visibility_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: voucher_logs
DROP TABLE IF EXISTS `voucher_logs`;
CREATE TABLE `voucher_logs` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `from_status` varchar(50) NOT NULL,
  `to_status` varchar(50) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `comment` text,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_voucher_logs_voucher_id` (`voucher_id`),
  KEY `idx_voucher_logs_created_at` (`created_at`),
  CONSTRAINT `voucher_logs_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `voucher_logs_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: voucher_relationships
DROP TABLE IF EXISTS `voucher_relationships`;
CREATE TABLE `voucher_relationships` (
  `id` int NOT NULL AUTO_INCREMENT,
  `original_voucher_id` varchar(255) NOT NULL,
  `related_voucher_id` varchar(255) NOT NULL,
  `relationship_type` enum('REJECTION_COPY','RESUBMISSION','CORRECTION') NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_original_voucher` (`original_voucher_id`),
  KEY `idx_related_voucher` (`related_voucher_id`),
  KEY `idx_relationship_type` (`relationship_type`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: voucher_sequences
DROP TABLE IF EXISTS `voucher_sequences`;
CREATE TABLE `voucher_sequences` (
  `department` varchar(50) NOT NULL,
  `month_year` varchar(10) NOT NULL,
  `next_number` int NOT NULL DEFAULT '1',
  `last_updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`department`),
  UNIQUE KEY `unique_dept_month` (`department`,`month_year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: vouchers
DROP TABLE IF EXISTS `vouchers`;
CREATE TABLE `vouchers` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(50) NOT NULL,
  `date` varchar(50) NOT NULL,
  `claimant` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `currency` enum('GHS','USD','GBP','EUR','CFA') NOT NULL,
  `department` varchar(50) NOT NULL,
  `dispatched_by` varchar(255) DEFAULT NULL,
  `dispatch_time` varchar(50) DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `sent_to_audit` tinyint(1) DEFAULT '0',
  `created_by` varchar(255) NOT NULL,
  `batch_id` varchar(36) DEFAULT NULL,
  `received_by` varchar(255) DEFAULT NULL,
  `receipt_time` varchar(50) DEFAULT NULL,
  `comment` text,
  `tax_type` varchar(50) DEFAULT NULL,
  `tax_details` text,
  `tax_amount` decimal(15,2) DEFAULT NULL,
  `pre_audited_amount` decimal(15,2) DEFAULT NULL,
  `pre_audited_by` varchar(255) DEFAULT NULL,
  `certified_by` varchar(255) DEFAULT NULL,
  `audit_dispatch_time` varchar(50) DEFAULT NULL,
  `audit_dispatched_by` varchar(255) DEFAULT NULL,
  `dispatch_to_on_department` tinyint(1) DEFAULT '0',
  `post_provisional_cash` tinyint(1) DEFAULT '0',
  `dispatched` tinyint(1) DEFAULT '0',
  `dispatch_to_audit_by` varchar(255) DEFAULT NULL,
  `is_returned` tinyint(1) DEFAULT '0',
  `return_comment` text,
  `return_time` varchar(50) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT '0',
  `deletion_time` varchar(50) DEFAULT NULL,
  `rejection_time` varchar(50) DEFAULT NULL,
  `department_receipt_time` varchar(50) DEFAULT NULL,
  `department_received_by` varchar(255) DEFAULT NULL,
  `department_rejected` tinyint(1) DEFAULT '0',
  `rejected_by` varchar(255) DEFAULT NULL,
  `pending_return` tinyint(1) DEFAULT '0',
  `return_initiated_time` varchar(50) DEFAULT NULL,
  `reference_id` varchar(50) DEFAULT NULL,
  `idempotency_key` varchar(255) DEFAULT NULL,
  `flags` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `original_department` varchar(50) DEFAULT NULL,
  `received_by_audit` tinyint(1) DEFAULT '0',
  `work_started` tinyint(1) DEFAULT '0',
  `batch_processed` tinyint(1) DEFAULT '0',
  `batch_processed_time` timestamp NULL DEFAULT NULL,
  `rejection_type` varchar(50) DEFAULT NULL,
  `parent_voucher_id` varchar(255) DEFAULT NULL,
  `version` int NOT NULL DEFAULT '1',
  `is_rejection_copy` tinyint(1) DEFAULT '0',
  `rejection_workflow_stage` enum('INITIAL','REJECTED_DUAL','DISPATCHED','RECEIVED_BY_FINANCE','RESUBMITTED','RESUBMITTED_CERTIFIED','PENDING_DISPATCH','FINANCE_PERMANENT','DISPATCHED_TO_FINANCE') DEFAULT 'INITIAL',
  `correction_count` int DEFAULT '0',
  `last_corrected_by` varchar(255) DEFAULT NULL,
  `last_correction_time` datetime DEFAULT NULL,
  `correction_reason` text,
  `is_corrected` tinyint(1) DEFAULT '0',
  `resubmission_count` int DEFAULT '0',
  `original_rejection_date` datetime DEFAULT NULL COMMENT 'Date when this voucher was first rejected',
  `resubmission_history` json DEFAULT NULL COMMENT 'History of resubmissions with dates and reasons',
  `resubmission_badge` varchar(100) DEFAULT NULL COMMENT 'Badge text to display for resubmitted vouchers',
  `original_rejection_reason` text,
  `resubmission_status` varchar(255) DEFAULT NULL,
  `original_rejected_by` varchar(255) DEFAULT NULL,
  `last_resubmitted_by` varchar(255) DEFAULT NULL,
  `last_resubmission_date` datetime DEFAULT NULL,
  `resubmission_comment` text,
  `is_resubmitted_voucher` tinyint(1) DEFAULT '0',
  `workflow_state` varchar(100) DEFAULT NULL,
  `badge_type` varchar(20) DEFAULT 'NONE',
  `is_copy` tinyint(1) DEFAULT '0',
  `workflow_history` json DEFAULT NULL,
  `rejection_audit_trail` json DEFAULT NULL,
  `resubmission_audit_trail` json DEFAULT NULL,
  `is_rejected` tinyint(1) DEFAULT '0',
  `original_voucher_id` varchar(255) DEFAULT NULL,
  `voucher_type` enum('ORIGINAL','COPY') DEFAULT 'ORIGINAL',
  `is_returned_copy` tinyint(1) DEFAULT '0',
  `returned_by` varchar(255) DEFAULT NULL,
  `is_resubmitted` tinyint(1) DEFAULT '0',
  `is_resubmission` tinyint(1) DEFAULT '0',
  `finance_received` tinyint(1) DEFAULT '0' COMMENT 'Track if Finance has received the voucher from Audit',
  `resubmission_certified_visible_to_finance` tinyint(1) DEFAULT '0',
  `resubmission_tracking_visible_to_audit` tinyint(1) DEFAULT '0',
  `badge_persistence_flags` json DEFAULT NULL,
  `is_returned_voucher` tinyint(1) DEFAULT '0',
  `return_count` int DEFAULT '0',
  `last_return_date` datetime DEFAULT NULL,
  `return_certified_visible_to_finance` tinyint(1) DEFAULT '0',
  `return_audit_visible` tinyint(1) DEFAULT '0',
  `original_return_reason` text,
  `original_returned_by` varchar(255) DEFAULT NULL,
  `last_edit_reason` text,
  `last_edited_by` varchar(255) DEFAULT NULL,
  `last_edit_time` datetime DEFAULT NULL,
  `is_on_hold` tinyint(1) DEFAULT '0' COMMENT 'Whether the voucher is currently on hold',
  `hold_comment` text COMMENT 'Reason why the voucher is on hold',
  `hold_by` varchar(255) DEFAULT NULL COMMENT 'User who put the voucher on hold',
  `hold_time` timestamp NULL DEFAULT NULL COMMENT 'When the voucher was put on hold',
  PRIMARY KEY (`id`),
  UNIQUE KEY `voucher_id` (`voucher_id`),
  KEY `idx_idempotency` (`created_by`,`department`,`idempotency_key`),
  KEY `idx_vouchers_rejection_type` (`rejection_type`),
  KEY `idx_vouchers_parent_voucher_id` (`parent_voucher_id`),
  KEY `idx_vouchers_rejection_copy` (`is_rejection_copy`),
  KEY `idx_vouchers_workflow_stage` (`rejection_workflow_stage`),
  KEY `idx_vouchers_dept_status_rejection` (`department`,`status`,`rejection_type`),
  KEY `idx_vouchers_orig_dept_status` (`original_department`,`status`),
  KEY `idx_vouchers_workflow_stage_dept` (`rejection_workflow_stage`,`department`),
  KEY `idx_vouchers_correction_count` (`correction_count`),
  KEY `idx_vouchers_is_corrected` (`is_corrected`),
  KEY `idx_vouchers_last_correction_time` (`last_correction_time`),
  KEY `idx_vouchers_resubmission_count` (`resubmission_count`),
  KEY `idx_vouchers_original_rejection_date` (`original_rejection_date`),
  KEY `idx_vouchers_workflow_state` (`workflow_state`),
  KEY `idx_vouchers_parent_id` (`parent_voucher_id`),
  KEY `idx_vouchers_workflow_department` (`workflow_state`,`department`),
  KEY `idx_vouchers_workflow_original_dept` (`workflow_state`,`original_department`),
  KEY `idx_vouchers_badge_type` (`badge_type`),
  KEY `idx_vouchers_is_copy` (`is_copy`),
  KEY `idx_vouchers_version` (`version`),
  KEY `idx_vouchers_is_resubmitted` (`is_resubmitted`),
  KEY `idx_vouchers_resubmission_visibility` (`resubmission_certified_visible_to_finance`,`resubmission_tracking_visible_to_audit`),
  KEY `idx_vouchers_department` (`department`),
  KEY `idx_vouchers_status` (`status`),
  KEY `idx_vouchers_created_at` (`created_at`),
  KEY `idx_vouchers_dept_status` (`department`,`status`),
  KEY `idx_vouchers_dept_status_created` (`department`,`status`,`created_at`),
  KEY `idx_vouchers_workflow_dept` (`workflow_state`,`department`),
  KEY `idx_vouchers_hold_status` (`is_on_hold`,`department`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Vouchers table with hold functionality for audit workflow management';

-- Data for table: vouchers
INSERT INTO `vouchers` VALUES
('11b55772-5404-4a71-a1e9-b8250046da49', 'PENAUG0008', 'AUGUST 02, 2025 AT 07:10 AM', 'VENESSA', 'TESTERING', '45000.00', 'GHS', 'PENSIONS', '', '', 'PENDING SUBMISSION', 0, 'JERRY JOHN', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'PENSIONS-1754118613258-8q9s8ycfn', NULL, '2025-08-02 07:10:13', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('16512b3d-de59-45d6-88c0-652ba356c3c1', 'PENAUG0002', 'AUGUST 02, 2025 AT 06:25 AM', 'NATHAN', 'TEST', '70000.00', 'GHS', 'PENSIONS', '', '', 'PENDING SUBMISSION', 0, 'JERRY JOHN', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 1, '2025-08-02 07:00:30', NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'PENSIONS-1754115951128-hrj5e6sjk', NULL, '2025-08-02 06:25:51', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('24822b97-1bc3-4813-a4d1-4c6b4577b17e', 'PENAUG0004', 'AUGUST 02, 2025 AT 06:56 AM', 'fairy tale', 'TEST', '7000.00', 'GHS', 'PENSIONS', '', '', 'PENDING SUBMISSION', 0, 'JERRY JOHN', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 1, '2025-08-02 07:00:18', NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'PENSIONS-1754117785247-pva0uy043', NULL, '2025-08-02 06:56:25', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('2f6cab1d-c723-422b-be10-bfa5a9c30fb5', 'FINAUG0005', 'AUGUST 02, 2025 AT 06:04 AM', 'GETTY', 'TEST', '81000.00', 'GHS', 'FINANCE', '', '', 'PENDING SUBMISSION', 0, 'MR. FELIX AYISI', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'FINANCE-1754114665914-0cpfo8coa', NULL, '2025-08-02 06:04:25', 'FINANCE', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('352f31a7-6f0c-488f-953e-63702882ed84', 'PENAUG0006', 'AUGUST 02, 2025 AT 07:04 AM', 'LARRY', 'TEST', '700.00', 'GHS', 'PENSIONS', '', '', 'PENDING SUBMISSION', 0, 'JERRY JOHN', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'PENSIONS-1754118281756-vubgh0osa', NULL, '2025-08-02 07:04:41', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('43ef86df-816b-4c07-881e-5cea65b0bad5', 'MISAUG0002', 'AUGUST 01, 2025 AT 08:00 PM', 'JOSH', 'TEST', '900000.00', 'CFA', 'AUDIT', '', '2025-08-01 20:01:03', 'AUDIT: PROCESSING', 1, 'CHARIS', 'd875c754-6a30-4fb6-9983-1d9271cd0687', 'EMMANUEL AMOAKOH', '2025-08-01 20:01:34', NULL, NULL, NULL, NULL, '100000.00', 'EMMANUEL AMOAKOH', 'EMMANUEL AMOAKOH', NULL, NULL, 0, 1, 0, 'CHARIS', 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'MISSIONS-1754035221946-8dngq0yku', [object Object], '2025-08-01 20:00:49', 'MISSIONS', 1, 1, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'AUDIT_PENDING_DISPATCH', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('485ba6f5-4a72-43d6-8941-01fcf1e6cb04', 'FINAUG0002', 'AUGUST 02, 2025 AT 05:47 AM', 'CLARK KENT', 'TEST', '56000.00', 'GHS', 'FINANCE', '', '', 'PENDING SUBMISSION', 0, 'MR. FELIX AYISI', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'FINANCE-1754113644714-m7vm9nohr', NULL, '2025-08-02 05:47:24', 'FINANCE', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('5f70093b-a73a-4718-8e73-c97b6ffab79e', 'PENAUG0005', 'AUGUST 02, 2025 AT 07:01 AM', ' KENT', 'TESTERing', '5000.00', 'GHS', 'PENSIONS', '', '', 'PENDING SUBMISSION', 0, 'JERRY JOHN', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 1, '2025-08-02 07:09:34', NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'PENSIONS-1754118070703-vlkzj1htq', NULL, '2025-08-02 07:01:10', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('6ef1e089-b534-43a1-87fb-52a2e3424975', 'PSOAUG0003-RETURN-COPY', 'AUGUST 02, 2025 AT 07:19 AM', 'KENPONG', 'RETURN COPY: TESTING', '63000.00', 'GHS', 'AUDIT', NULL, NULL, 'VOUCHER RETURNED', 1, 'GYAMPOH', NULL, NULL, NULL, 'INSUFFICIENT ATTACHMENT', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, '2025-08-02 07:20:51', 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2025-08-02 07:19:06', 'PENTSOS', 1, 0, 0, NULL, NULL, 'b235c938-994e-4e70-88fc-ee685482d393', 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'AUDIT_RETURNED_COPY', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 1, 'EMMANUEL AMOAKOH', 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('7ca31f51-b556-4235-a06c-5918c142fbf0', 'PSOAUG0001', 'AUGUST 02, 2025 AT 07:17 AM', 'CLARK KENT', 'test', '20000.00', 'USD', 'PENTSOS', '', '', 'PENDING SUBMISSION', 0, 'GYAMPOH', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'PENTSOS-1754119070854-a720wd6mr', NULL, '2025-08-02 07:17:50', 'PENTSOS', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('851a3036-b42a-4220-a32a-b5ceeaa8dbeb', 'PENAUG0003', 'AUGUST 02, 2025 AT 06:55 AM', 'KWOW', 'VEHICLE', '9000.00', 'GHS', 'PENSIONS', '', '', 'PENDING SUBMISSION', 0, 'JERRY JOHN', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 1, '2025-08-02 07:00:27', NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'PENSIONS-1754117703784-cpv370ajd', NULL, '2025-08-02 06:55:03', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('944d00a5-71b8-484f-a07a-718da38f66fe', 'PENAUG0001', 'AUGUST 02, 2025 AT 06:24 AM', 'JACK', 'REAPER', '70000.00', 'GHS', 'PENSIONS', '', '', 'PENDING SUBMISSION', 0, 'JERRY JOHN', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 1, '2025-08-02 07:09:33', NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'PENSIONS-1754115851929-uy448iren', NULL, '2025-08-02 06:24:12', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('b235c938-994e-4e70-88fc-ee685482d393', 'PSOAUG0003', 'AUGUST 02, 2025 AT 07:19 AM', 'KENPONG SAM', 'MEDICS PARTY', '90000.00', 'CFA', 'AUDIT', '', '2025-08-02 07:27:47', 'AUDIT: PROCESSING', 1, 'GYAMPOH', 'b99d4c7f-1699-4b9f-a4cb-b44a2496e99e', 'EMMANUEL AMOAKOH', '2025-08-02 07:28:02', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-02 07:20:51', 'EMMANUEL AMOAKOH', 0, 0, 0, 'GYAMPOH', 0, NULL, '2025-08-02 07:20:51', 0, NULL, NULL, '2025-08-02 07:21:16', 'GYAMPOH', 0, NULL, 0, NULL, NULL, 'PENTSOS-1754119145982-q1gqih9iw', [object Object], '2025-08-02 07:19:06', 'PENTSOS', 1, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, 'INSUFFICIENT ATTACHMENT', NULL, NULL, NULL, NULL, NULL, 0, 'AUDIT_NEW', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, 'EMMANUEL AMOAKOH', 0, 0, 0, 0, 0, NULL, 1, 1, '2025-08-02 07:20:51', 0, 0, 'INSUFFICIENT ATTACHMENT', 'EMMANUEL AMOAKOH', NULL, NULL, NULL, 0, NULL, NULL, NULL),
('c5554dcf-5831-462c-a702-b61b3a078813', 'PSOAUG0003-PENTSOS-RETURN-COPY', 'AUGUST 02, 2025 AT 07:19 AM', 'KENPONG', 'PENTSOS RETURN COPY: TESTING', '63000.00', 'GHS', 'PENTSOS', NULL, NULL, 'VOUCHER RETURNED', 1, 'GYAMPOH', NULL, NULL, NULL, 'INSUFFICIENT ATTACHMENT', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, '2025-08-02 07:20:51', 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2025-08-02 07:19:06', 'PENTSOS', 1, 0, 0, NULL, NULL, 'b235c938-994e-4e70-88fc-ee685482d393', 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'PENTSOS_RETURNED_COPY', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 1, 'EMMANUEL AMOAKOH', 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('dfae4241-8f1b-4042-88c1-ef04e92492e7', 'PENAUG0007', 'AUGUST 02, 2025 AT 07:07 AM', 'KENPONG', 'TESTER', '78000.00', 'GHS', 'PENSIONS', '', '', 'PENDING SUBMISSION', 0, 'JERRY JOHN', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'PENSIONS-1754118443484-xlybl9fis', NULL, '2025-08-02 07:07:23', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('e5f9a7f0-05ed-4a03-b92d-7c1392a8e0f4', 'FINAUG0001', 'AUGUST 01, 2025 AT 08:08 PM', 'JAMES', 'VEHICLE', '700000.00', 'GHS', 'FINANCE', '', '', 'PENDING SUBMISSION', 0, 'MR. FELIX AYISI', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'FINANCE-1754078924937-vyhj7ia00', NULL, '2025-08-01 20:08:45', 'FINANCE', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('e7f8f40a-91e5-41cf-92f8-2ac574cb6b34', 'FINAUG0003', 'AUGUST 02, 2025 AT 05:50 AM', 'fairy tale', 'test', '89600.00', 'GHS', 'AUDIT', '', '2025-08-02 05:50:47', 'AUDIT: PROCESSING', 1, 'MR. FELIX AYISI', '5273d456-474f-451c-8eb3-a305401b3d26', 'EMMANUEL AMOAKOH', '2025-08-02 06:06:04', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 'MR. FELIX AYISI', 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'FINANCE-1754113811747-9yjz291cp', [object Object], '2025-08-02 05:50:11', 'FINANCE', 1, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'AUDIT_NEW', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('ebc5d49e-2075-4d43-89a4-42d18428128b', 'PSOAUG0002', 'AUGUST 02, 2025 AT 07:18 AM', 'KWAME', 'TESTER', '78000.00', 'GHS', 'PENTSOS', '', '', 'PENDING SUBMISSION', 0, 'GYAMPOH', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'PENTSOS-1754119088052-19r1eppmc', NULL, '2025-08-02 07:18:08', 'PENTSOS', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('ee05fb30-4d80-4f34-abdb-1bac4391096c', 'FINAUG0004', 'AUGUST 02, 2025 AT 05:59 AM', 'NAT', 'TEST', '709000.00', 'CFA', 'FINANCE', '', '', 'PENDING SUBMISSION', 0, 'MR. FELIX AYISI', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'FINANCE-1754114372548-ecrv3fg46', NULL, '2025-08-02 05:59:33', 'FINANCE', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('f513d04d-aae1-4702-9057-5a9a87cbcdd8', 'PENAUG0009', 'AUGUST 02, 2025 AT 07:11 AM', 'KOJO', 'TEST', '84000.00', 'GHS', 'PENSIONS', '', '', 'PENDING SUBMISSION', 0, 'JERRY JOHN', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'PENSIONS-1754118662635-94dv353de', NULL, '2025-08-02 07:11:02', 'PENSIONS', 0, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_PENDING', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('f622a8e4-0fc1-4719-b39c-e8bd162b840d', 'MISAUG0001', 'AUGUST 01, 2025 AT 06:09 PM', 'MAX CHIDI', 'test', '85000.00', 'GHS', 'AUDIT', '', '2025-08-01 18:09:23', 'AUDIT: PROCESSING', 1, 'CHARIS', 'fd553b2e-769e-4942-bedd-e31f80a9d587', 'EMMANUEL AMOAKOH', '2025-08-01 18:09:29', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 'CHARIS', 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'MISSIONS-1754028527034-gh5vp15ez', [object Object], '2025-08-01 18:09:15', 'MISSIONS', 1, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'AUDIT_NEW', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 1, 'AWAITING APPROVAL FROM BOSS', 'EMMANUEL AMOAKOH', '2025-08-01 19:59:23');

-- Table: workflow_audit_log
DROP TABLE IF EXISTS `workflow_audit_log`;
CREATE TABLE `workflow_audit_log` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `from_state` varchar(50) NOT NULL,
  `to_state` varchar(50) NOT NULL,
  `event_type` varchar(50) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `copy_id` varchar(36) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_user_id` (`user_id`),
  KEY `copy_id` (`copy_id`),
  CONSTRAINT `workflow_audit_log_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `workflow_audit_log_ibfk_2` FOREIGN KEY (`copy_id`) REFERENCES `vouchers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: workflow_transitions
DROP TABLE IF EXISTS `workflow_transitions`;
CREATE TABLE `workflow_transitions` (
  `id` varchar(36) NOT NULL,
  `from_state` varchar(50) NOT NULL,
  `event_type` varchar(50) NOT NULL,
  `to_state` varchar(50) NOT NULL,
  `requires_copy` tinyint(1) DEFAULT '0',
  `copy_state` varchar(50) DEFAULT NULL,
  `badge_type` varchar(20) DEFAULT 'NONE',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_transition` (`from_state`,`event_type`),
  KEY `idx_from_state` (`from_state`),
  KEY `idx_event_type` (`event_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

SET FOREIGN_KEY_CHECKS = 1;
