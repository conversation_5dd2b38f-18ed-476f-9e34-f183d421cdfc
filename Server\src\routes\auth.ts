import express from 'express';
// Simple authentication without password hashing
import { v4 as uuidv4 } from 'uuid';
import { query } from '../database/db.js';
import { authenticate } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';
import { broadcastRegistrationUpdate } from '../socket/socketHandlers.js';
import { AuditService } from '../services/audit-service.js';
import { cleanupUserSessions } from '../utils/session-deduplication.js';

export const authRouter = express.Router();

// Simplified login route for internal LAN use
authRouter.post('/login', async (req, res) => {
  try {
    const { department, username, password } = req.body;

    // Basic validation
    if (!department || !username || !password) {
      return res.status(400).json({
        error: 'Please enter department, username, and password'
      });
    }

    // Normalize inputs for case-insensitive comparison
    const normalizedDepartment = department.toUpperCase().trim();
    const normalizedUsername = username.toUpperCase().trim();

    // PASSWORD CHANGE FIX: Handle trailing spaces in database names
    const users = await query(`
      SELECT id, name, password, department, role, is_active, last_login
      FROM users
      WHERE UPPER(TRIM(name)) = ? AND UPPER(TRIM(department)) = ? AND is_active = 1
    `, [normalizedUsername, normalizedDepartment]) as Array<{
      id: string;
      name: string;
      password: string;
      department: string;
      role: string;
      is_active: boolean;
      last_login: string;
    }>;

    if (users.length === 0) {
      logger.warn(`Login attempt failed - user not found: ${normalizedUsername} (${normalizedDepartment})`);
      return res.status(401).json({
        error: 'Username, department, or password incorrect. Please check and try again.'
      });
    }

    const user = users[0];

    // Direct password comparison (no hashing for internal LAN use)
    if (password !== user.password) {
      logger.warn(`Login attempt failed - wrong password: ${user.name} (${user.department})`);
      return res.status(401).json({
        error: 'Username, department, or password incorrect. Please check and try again.'
      });
    }

    // Update last login timestamp
    await query('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);

    logger.info(`✅ Successful login: ${user.name} (${user.department}) - Role: ${user.role}`);

    // Log successful login to audit trail
    try {
      await AuditService.logLogin(
        user.id,
        user.name,
        user.department,
        req.ip || 'unknown',
        req.get('User-Agent') || 'unknown'
      );
    } catch (auditError) {
      logger.warn('Failed to log login audit:', auditError);
      // Don't fail login if audit logging fails
    }

    // PRODUCTION FIX: Allow multiple concurrent sessions per user
    // Only clean up very old sessions (7+ days) to prevent database bloat
    await query(
      'DELETE FROM active_sessions WHERE is_active = FALSE AND session_end < DATE_SUB(NOW(), INTERVAL 7 DAY)'
    );

    // PRODUCTION FIX: Always create new session for better reliability
    // Multiple sessions per user are allowed for production flexibility
    const sessionId = uuidv4();

    await query(
      `INSERT INTO active_sessions (id, user_id, user_name, department, session_start, last_activity, is_active)
       VALUES (?, ?, ?, ?, NOW(), NOW(), TRUE)`,
      [sessionId, user.id, user.name, user.department]
    );

    logger.info(`Created new session for user ${user.name} (session: ${sessionId})`);

    // Optional: Clean up only very old sessions (30+ days) for this user
    await query(
      `UPDATE active_sessions SET is_active = FALSE, session_end = NOW()
       WHERE user_id = ? AND is_active = TRUE AND session_start < DATE_SUB(NOW(), INTERVAL 30 DAY)`,
      [user.id]
    );

    // Set session cookie for browser (FIXED: Use consistent cookie name)
    res.cookie('vms_session_id', sessionId, {
      httpOnly: true,
      secure: false, // HTTP for LAN
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      sameSite: 'lax'
    });

    // Return user data with session ID for client storage
    return res.json({
      success: true,
      sessionId: sessionId, // Send session ID instead of JWT token
      user: {
        id: user.id,
        name: user.name,
        department: user.department,
        role: user.role,
        lastLogin: new Date().toISOString()
      },
      message: 'Login successful - Welcome to VMS'
    });

  } catch (error) {
    logger.error('Login system error:', error);
    return res.status(500).json({
      error: 'System error. Please try again or contact IT support.'
    });
  }
});

// Register route
authRouter.post('/register', async (req, res) => {
  try {
    const { name, password, department } = req.body;

    // Validate input
    if (!name || !password || !department) {
      return res.status(400).json({ error: 'Name, password, and department are required' });
    }

    // Create a new pending registration
    const registrationId = uuidv4();
    const normalizedName = name.toUpperCase();
    const normalizedDepartment = department.toUpperCase();

    // SIMPLIFIED: Store password directly without hashing
    console.log(`Storing password directly: ${password}`);

    // No need for verification since we're storing the password as-is

    // Log for debugging
    console.log(`Creating new registration: ${normalizedName} (${normalizedDepartment}) with ID: ${registrationId}`);

    await query(
      'INSERT INTO pending_registrations (id, name, password, department, date_requested, status) VALUES (?, ?, ?, ?, NOW(), ?)',
      [registrationId, normalizedName, password, normalizedDepartment, 'pending']
    );

    // Create notification for admin users
    const notificationId = uuidv4();
    await query(
      'INSERT INTO notifications (id, user_id, message, is_read, timestamp, type) VALUES (?, ?, ?, ?, NOW(), ?)',
      [notificationId, 'admin', `New user registration: ${normalizedName} (${normalizedDepartment})`, false, 'OTHER']
    );

    // Create registration object for broadcasting
    const newRegistration = {
      id: registrationId,
      name: normalizedName,
      department: normalizedDepartment,
      dateRequested: new Date().toISOString(),
      status: 'pending'
    };

    // Broadcast registration update to all connected clients
    broadcastRegistrationUpdate('created', newRegistration);

    // Log the broadcast
    console.log(`Broadcasting new registration: ${registrationId}`);

    // Verify the registration was added
    const verifyRegistrations = await query('SELECT * FROM pending_registrations WHERE id = ?', [registrationId]) as any[];
    console.log(`Verification: Found ${verifyRegistrations.length} registrations with ID ${registrationId}`);

    res.status(201).json({
      message: 'Registration request submitted successfully',
      registrationId
    });
  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({ error: 'Registration failed' });
  }
});

// Get current user route
authRouter.get('/me', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user from database
    const users = await query('SELECT * FROM users WHERE id = ?', [userId]) as any[];

    if (users.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = users[0];

    // Return user data with proper field mapping
    res.json({
      id: user.id,
      name: user.name,
      department: user.department,
      role: user.role,
      lastLogin: user.last_login,
      dateCreated: user.date_created,
      isActive: Boolean(user.is_active)
    });
  } catch (error) {
    logger.error('Get current user error:', error);
    res.status(500).json({ error: 'Failed to get user data' });
  }
});

// Logout route - now handles session termination
authRouter.post('/logout', authenticate, async (req, res) => {
  try {
    // CRITICAL FIX: Get the session ID from the token
    const sessionId = req.user.sessionId;

    if (!sessionId) {
      logger.warn(`User ${req.user.name} (${req.user.id}) attempted to logout without a valid session ID`);
      return res.status(400).json({ error: 'No active session found' });
    }

    // PRESENCE CLEANUP FIX: Force cleanup of user's resource locks on logout
    try {
      const { releaseUserLocksOnLogout } = require('../socket/socketHandlers');
      const releasedLocks = releaseUserLocksOnLogout(req.user.id);
      if (releasedLocks > 0) {
        logger.info(`🧹 Logout cleanup: Released ${releasedLocks} resource locks for user ${req.user.name} (${req.user.id})`);
      }
    } catch (error) {
      logger.warn('Failed to cleanup resource locks on logout:', error);
      // Don't fail logout if lock cleanup fails
    }

    // Mark the session as inactive
    await query(
      'UPDATE active_sessions SET is_active = FALSE, session_end = NOW() WHERE id = ?',
      [sessionId]
    );

    // Log logout to audit trail
    try {
      await AuditService.logLogout(
        req.user.id,
        req.user.name,
        req.user.department,
        req.ip || 'unknown',
        req.get('User-Agent') || 'unknown'
      );
    } catch (auditError) {
      logger.warn('Failed to log logout audit:', auditError);
      // Don't fail logout if audit logging fails
    }

    logger.info(`User ${req.user.name} (${req.user.id}) logged out successfully. Session ID: ${sessionId}`);

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({ error: 'Logout failed' });
  }
});

// ENHANCED: Immediate logout endpoint for browser close detection
authRouter.post('/immediate-logout', async (req, res) => {
  try {
    const { userId, sessionId, reason, timestamp } = req.body;

    // FIXED: Safe timestamp handling to prevent Invalid time value error
    let timeString = 'unknown';
    try {
      if (timestamp && !isNaN(timestamp)) {
        timeString = new Date(Number(timestamp)).toISOString();
      } else {
        timeString = new Date().toISOString();
      }
    } catch (timeError) {
      timeString = new Date().toISOString();
    }

    logger.info(`🚪 IMMEDIATE LOGOUT: User ${userId} (${reason}) at ${timeString}`);

    if (sessionId && sessionId !== 'unknown') {
      // Check if session exists before deactivating
      const sessionCheck = await query('SELECT id, user_id, user_name FROM active_sessions WHERE id = ? AND is_active = TRUE', [sessionId]);

      if (sessionCheck.length > 0) {
        // Immediately deactivate session
        await query('UPDATE active_sessions SET is_active = FALSE, logout_reason = ?, session_end = NOW() WHERE id = ?', [reason, sessionId]);
        logger.info(`✅ IMMEDIATE LOGOUT: Session ${sessionId} found and deactivated`);
      } else {
        logger.warn(`⚠️ IMMEDIATE LOGOUT: Session ${sessionId} not found or already inactive`);
      }

      // Clean up user connections and locks immediately (regardless of session status)
      try {
        const { releaseUserLocksOnLogout } = require('../socket/socketHandlers');
        const cleanedUp = await releaseUserLocksOnLogout(userId);
        logger.info(`✅ IMMEDIATE LOGOUT: User ${userId} cleanup completed - ${cleanedUp} locks released`);
      } catch (lockError) {
        logger.warn('Failed to cleanup locks on immediate logout:', lockError);
      }
    } else {
      logger.warn(`⚠️ IMMEDIATE LOGOUT: No valid sessionId provided for user ${userId}`);

      // Still try to cleanup user connections even without sessionId
      try {
        const { releaseUserLocksOnLogout } = require('../socket/socketHandlers');
        const cleanedUp = await releaseUserLocksOnLogout(userId);
        logger.info(`✅ IMMEDIATE LOGOUT: User ${userId} cleanup completed without session - ${cleanedUp} locks released`);
      } catch (lockError) {
        logger.warn('Failed to cleanup locks on immediate logout:', lockError);
      }
    }

    // Always respond quickly for sendBeacon
    res.status(200).json({ success: true });
  } catch (error) {
    logger.error('❌ Immediate logout error:', error);
    // Still respond with success to avoid beacon retries
    res.status(200).json({ success: false, error: error instanceof Error ? error.message : String(error) });
  }
});

// Get users by department (public endpoint for login page)
authRouter.get('/users-by-department', async (req, res) => {
  try {
    // Simple query for vms_production database schema
    const users = await query(`
      SELECT id, name, department
      FROM users
      ORDER BY department, name
    `) as any[];

    logger.info(`Fetched ${users.length} users for login dropdown`);

    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    res.json(users);
  } catch (error) {
    logger.error('Get users by department error:', error);
    res.status(500).json({ error: 'Failed to get users' });
  }
});

// REMOVED: All duplicate endpoints that were causing authentication conflicts
// Only the proper authenticated endpoints above should be used
