
import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Department, Voucher } from '@/lib/types';
import { VoucherListControls } from '@/components/dashboard/voucher-list-controls';
import { VoucherTabs } from '@/components/dashboard/voucher-tabs';
// REMOVED: Legacy VoucherBatchNotification - using new notification system only
import { DepartmentNewlyArrivedVouchers } from '@/components/dashboard/department-newly-arrived-vouchers';
import { useDepartmentData } from '@/hooks/use-department-data';
import { useDepartmentUsers } from '@/hooks/use-department-users';
// PENDING TAB EDITING: Import voucher editing hook
import { useVoucherEditing } from '@/components/department-voucher-hub/hooks/use-voucher-editing';

interface DashboardContentProps {
  department: Department;
  refreshTrigger: number;
  onRefresh: () => void;
  onReceiveVouchers: (voucherIdsOrBatchId: string[] | string, isBatchId?: boolean) => void;
  selectedVouchers: string[];
  dispatchedBy: string;
  customDispatchName: string;
  onDispatcherChange: (value: string) => void;
  onCustomDispatchNameChange: (value: string) => void;
  onSendToAudit: () => void;
  onSelectionChange: (selectedIds: string[]) => void;
  onViewVoucher: (voucher: Voucher) => void;
  voucherView: string;
  onVoucherViewChange: (view: string) => void;
  isNotificationBlinking: boolean;
}

export function DashboardContent({
  department,
  refreshTrigger,
  onRefresh,
  onReceiveVouchers,
  selectedVouchers,
  dispatchedBy,
  customDispatchName,
  onDispatcherChange,
  onCustomDispatchNameChange,
  onSendToAudit,
  onSelectionChange,
  onViewVoucher,
  voucherView,
  onVoucherViewChange,
  isNotificationBlinking
}: DashboardContentProps) {
  const {
    pendingSubmissionVouchers,
    processingVouchers,
    certifiedVouchers,
    rejectedVouchers,
    returnedVouchers,
    batchesArray
  } = useDepartmentData(department, refreshTrigger);

  const departmentUsers = useDepartmentUsers(department);

  // PENDING TAB EDITING: Add voucher editing functionality
  const {
    voucherEdits,
    handleVoucherEdit,
    handleSaveVoucherEdits
  } = useVoucherEditing();

  // REAL-TIME VOUCHER CREATION FIX: Listen for new voucher creation events
  useEffect(() => {
    const handleVoucherCreated = (event: CustomEvent) => {
      const eventType = event.detail?.type;
      console.log(`🔄 REAL-TIME CREATION: Dashboard received voucher event: ${eventType}`);

      // Force refresh for voucher creation events
      if (eventType === 'created') {
        console.log(`🔄 REAL-TIME CREATION: New voucher created, triggering immediate refresh for ${department}`);

        // Trigger immediate refresh by updating the refresh trigger
        onRefresh();

        // Also dispatch a custom event to ensure all components refresh
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('voucherListRefresh', {
            detail: {
              department,
              type: 'voucher_created',
              timestamp: Date.now()
            }
          }));
        }, 100);
      }
    };

    window.addEventListener('voucherUpdated', handleVoucherCreated as EventListener);
    return () => {
      window.removeEventListener('voucherUpdated', handleVoucherCreated as EventListener);
    };
  }, [department, onRefresh]);

  // PRODUCTION FIX: Detect vouchers to receive including those with VOUCHER PROCESSING status
  const hasVouchersToReceive = batchesArray && batchesArray.length > 0 &&
    batchesArray.some(batch =>
      batch.vouchers && batch.vouchers.some(v => {
        const isProcessedVoucher = (
          v.certifiedBy ||
          v.status === "VOUCHER REJECTED" ||
          v.status === "VOUCHER PROCESSING" ||  // NEW: Vouchers dispatched from Audit
          v.isReturned ||
          v.pendingReturn ||
          (v.dispatched && v.auditDispatchedBy)  // Additional check for dispatched vouchers
        );

        // PRODUCTION FIX: Only show if not yet received by department
        const notYetReceived = !v.departmentReceiptTime;

        return isProcessedVoucher && notYetReceived;
      })
    );

  // REMOVED: handleReceiveAllVouchers function - no longer needed with new notification system

  return (
    <main className="flex-1 flex flex-col overflow-hidden bg-black">
      <div className="px-6 pb-6 flex flex-col h-full">
        <div className="flex-1 flex flex-col min-h-0">
          {/* Show prominent NEWLY ARRIVED VOUCHERS section when there are batches from audit */}
          {batchesArray.length > 0 && (
            <div className="mb-6">
              <DepartmentNewlyArrivedVouchers
                batchesArray={batchesArray}
                onReceiveVouchers={(batchId) => onReceiveVouchers(batchId, true)}
              />
            </div>
          )}

          {/* REMOVED: Legacy VoucherBatchNotification - using new notification system only */}

          <Card className="bg-[#0a0a0a] border-gray-800 flex-1 flex flex-col overflow-hidden">
            <CardContent className="p-4 flex flex-col flex-1">
              {/* TEMPORARILY DISABLED TO TEST INFINITE LOOP */}
              {false && <VoucherListControls
                voucherView={voucherView}
                selectedVouchers={selectedVouchers}
                dispatchedBy={dispatchedBy}
                customDispatchName={customDispatchName}
                departmentUsers={departmentUsers}
                onSendToAudit={onSendToAudit}
                onDispatcherChange={onDispatcherChange}
                onCustomDispatchNameChange={onCustomDispatchNameChange}
                isDisabled={hasVouchersToReceive}
              />}

              <VoucherTabs
                voucherView={voucherView}
                onVoucherViewChange={onVoucherViewChange}
                pendingSubmissionVouchers={pendingSubmissionVouchers}
                processingVouchers={processingVouchers}
                certifiedVouchers={certifiedVouchers}
                rejectedVouchers={rejectedVouchers}
                returnedVouchers={returnedVouchers}
                department={department}
                onSelectionChange={onSelectionChange}
                onViewVoucher={onViewVoucher}
                isDisabled={hasVouchersToReceive}
                // PENDING TAB EDITING: Pass editing props
                enablePendingEditing={true}
                voucherEdits={voucherEdits}
                onVoucherEdit={handleVoucherEdit}
                onSaveVoucherEdits={handleSaveVoucherEdits}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  );
}
