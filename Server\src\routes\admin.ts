import express from 'express';
import fs from 'fs';
import path from 'path';
import pool, { query } from '../database/db.js';
import { authenticate, authorize } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';
import { Router } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { yearRolloverService } from '../services/year-rollover-service.js';
import { backupScheduler } from '../services/backup-scheduler.js';

export const adminRouter = express.Router();

// Apply authentication middleware to all routes
adminRouter.use(authenticate);
// Apply authorization middleware to all routes
adminRouter.use(authorize(['admin']));

// Get system settings
adminRouter.get('/settings', async (req, res) => {
  try {
    const settings = await query('SELECT * FROM system_settings LIMIT 1') as any[];
    
    if (settings.length === 0) {
      // Create default settings if none exist
      const currentYear = new Date().getFullYear();
      await query(
        `INSERT INTO system_settings (
          fiscal_year_start, fiscal_year_end, current_fiscal_year, system_time, auto_backup_enabled, session_timeout
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        ['JAN', 'DEC', currentYear, new Date().toISOString(), true, 30]
      );
      
      const newSettings = await query('SELECT * FROM system_settings LIMIT 1') as any[];
      res.json(newSettings[0]);
    } else {
      res.json(settings[0]);
    }
  } catch (error) {
    logger.error('Get system settings error:', error);
    res.status(500).json({ error: 'Failed to get system settings' });
  }
});

// Update system settings
adminRouter.put('/settings', async (req, res) => {
  try {
    const {
      fiscalYearStart,
      fiscalYearEnd,
      currentFiscalYear,
      systemTime,
      autoBackupEnabled,
      sessionTimeout
    } = req.body;
    
    // Check if settings exist
    const settings = await query('SELECT * FROM system_settings LIMIT 1') as any[];
    
    if (settings.length === 0) {
      // Create settings if none exist
      await query(
        `INSERT INTO system_settings (
          fiscal_year_start, fiscal_year_end, current_fiscal_year, system_time, auto_backup_enabled, session_timeout
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        [
          fiscalYearStart || 'JAN',
          fiscalYearEnd || 'DEC',
          currentFiscalYear || new Date().getFullYear(),
          systemTime || new Date().toISOString(),
          autoBackupEnabled !== undefined ? autoBackupEnabled : true,
          sessionTimeout || 30
        ]
      );
    } else {
      // Update existing settings
      let updateQuery = 'UPDATE system_settings SET ';
      const updateParams = [];
      const updates = [];
      
      if (fiscalYearStart !== undefined) {
        updates.push('fiscal_year_start = ?');
        updateParams.push(fiscalYearStart);
      }
      
      if (fiscalYearEnd !== undefined) {
        updates.push('fiscal_year_end = ?');
        updateParams.push(fiscalYearEnd);
      }
      
      if (currentFiscalYear !== undefined) {
        updates.push('current_fiscal_year = ?');
        updateParams.push(currentFiscalYear);
      }
      
      if (systemTime !== undefined) {
        updates.push('system_time = ?');
        updateParams.push(systemTime);
      }
      
      if (autoBackupEnabled !== undefined) {
        updates.push('auto_backup_enabled = ?');
        updateParams.push(autoBackupEnabled);
      }
      
      if (sessionTimeout !== undefined) {
        updates.push('session_timeout = ?');
        updateParams.push(sessionTimeout);
      }
      
      // If no updates, return early
      if (updates.length === 0) {
        return res.status(400).json({ error: 'No updates provided' });
      }
      
      updateQuery += updates.join(', ') + ' WHERE id = ?';
      updateParams.push(settings[0].id);
      
      await query(updateQuery, updateParams);
    }
    
    // Get updated settings
    const updatedSettings = await query('SELECT * FROM system_settings LIMIT 1') as any[];
    
    res.json(updatedSettings[0]);
  } catch (error) {
    logger.error('Update system settings error:', error);
    res.status(500).json({ error: 'Failed to update system settings' });
  }
});

// Backup database
adminRouter.post('/backup', async (req, res) => {
  try {
    // Create backups directory if it doesn't exist
    const backupsDir = path.join(process.cwd(), 'backups');
    if (!fs.existsSync(backupsDir)) {
      fs.mkdirSync(backupsDir);
    }
    
    // Generate backup filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFilename = `vms_backup_${timestamp}.sql`;
    const backupPath = path.join(backupsDir, backupFilename);
    
    // Get database connection config
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production',
    };
    
    // PRODUCTION-GRADE: Node.js-based backup (no external dependencies)
    try {
      // Create database connection for backup
      const mysql = require('mysql2/promise');
      const connection = await mysql.createConnection(dbConfig);

      // Get all table names
      const [tables] = await connection.execute('SHOW TABLES');
      const tableNames = tables.map((row: any) => Object.values(row)[0]);

      let backupContent = `-- VMS Production Database Backup\n`;
      backupContent += `-- Generated: ${new Date().toISOString()}\n`;
      backupContent += `-- Database: ${dbConfig.database}\n\n`;
      backupContent += `SET FOREIGN_KEY_CHECKS = 0;\n\n`;

      // Backup each table
      for (const tableName of tableNames) {
        // Get table structure
        const [createTable] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
        const createStatement = (createTable as any)[0]['Create Table'];

        backupContent += `-- Table: ${tableName}\n`;
        backupContent += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
        backupContent += `${createStatement};\n\n`;

        // Get table data
        const [rows] = await connection.execute(`SELECT * FROM \`${tableName}\``);
        if ((rows as any[]).length > 0) {
          backupContent += `-- Data for table: ${tableName}\n`;
          backupContent += `INSERT INTO \`${tableName}\` VALUES\n`;

          const values = (rows as any[]).map(row => {
            const escapedValues = Object.values(row).map(value => {
              if (value === null) return 'NULL';
              if (typeof value === 'string') {
                // Handle datetime strings that might be in ISO format
                if (value.includes('T') && value.includes('Z')) {
                  try {
                    // Convert ISO datetime to MySQL format
                    const date = new Date(value);
                    // Check if the date is valid
                    if (isNaN(date.getTime())) {
                      // If invalid date, treat as regular string
                      return `'${value.replace(/'/g, "''")}'`;
                    }
                    return `'${date.toISOString().slice(0, 19).replace('T', ' ')}'`;
                  } catch (error) {
                    // If any error occurs, treat as regular string
                    return `'${value.replace(/'/g, "''")}'`;
                  }
                }
                return `'${value.replace(/'/g, "''")}'`;
              }
              if (value instanceof Date) {
                try {
                  // Check if the date is valid before converting
                  if (isNaN(value.getTime())) {
                    return 'NULL';
                  }
                  return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
                } catch (error) {
                  return 'NULL';
                }
              }
              return value;
            });
            return `(${escapedValues.join(', ')})`;
          });

          backupContent += values.join(',\n') + ';\n\n';
        }
      }

      backupContent += `SET FOREIGN_KEY_CHECKS = 1;\n`;

      // Write backup file
      fs.writeFileSync(backupPath, backupContent, 'utf8');

      // Verify backup file was created and has content
      const stats = fs.statSync(backupPath);
      if (stats.size === 0) {
        throw new Error('Backup file is empty');
      }

      await connection.end();

      // Update last backup date in system settings
      await query(
        'UPDATE system_settings SET last_backup_date = ? WHERE id = 1',
        [new Date().toISOString()]
      );

      logger.info(`✅ Backup created successfully: ${backupFilename} (${stats.size} bytes)`);

      res.json({
        message: 'Backup created successfully',
        filename: backupFilename,
        path: backupPath,
        size: stats.size,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('❌ Backup creation failed:', error);

      // Clean up failed backup file
      if (fs.existsSync(backupPath)) {
        fs.unlinkSync(backupPath);
      }

      res.status(500).json({
        error: 'Failed to create backup',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  } catch (error) {
    logger.error('Backup error:', error);
    res.status(500).json({ error: 'Failed to create backup' });
  }
});

// Restore database from backup
adminRouter.post('/restore', async (req, res) => {
  try {
    const { filename } = req.body;
    
    if (!filename) {
      return res.status(400).json({ error: 'Backup filename is required' });
    }
    
    // Check if backup file exists
    const backupPath = path.join(process.cwd(), 'backups', filename);
    if (!fs.existsSync(backupPath)) {
      return res.status(404).json({ error: 'Backup file not found' });
    }
    
    // Get database connection config
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production',
    };
    
    // PRODUCTION-GRADE: Node.js-based restore with validation
    try {
      // Validate backup file
      const backupContent = fs.readFileSync(backupPath, 'utf8');
      if (!backupContent.includes('-- VMS Production Database Backup')) {
        return res.status(400).json({ error: 'Invalid backup file format' });
      }

      // Create database connection for restore
      const mysql = require('mysql2/promise');
      const connection = await mysql.createConnection(dbConfig);

      // Split backup into individual statements
      const statements = backupContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      logger.info(`🔄 Starting restore process: ${statements.length} statements to execute`);

      // Execute statements in transaction for safety
      await connection.beginTransaction();

      try {
        let executedCount = 0;
        for (const statement of statements) {
          if (statement.trim()) {
            await connection.execute(statement);
            executedCount++;
          }
        }

        await connection.commit();
        await connection.end();

        logger.info(`✅ Database restored successfully: ${executedCount} statements executed`);

        res.json({
          message: 'Database restored successfully',
          statementsExecuted: executedCount,
          timestamp: new Date().toISOString()
        });

      } catch (execError) {
        await connection.rollback();
        await connection.end();
        throw execError;
      }

    } catch (error) {
      logger.error('❌ Restore failed:', error);
      res.status(500).json({
        error: 'Failed to restore backup',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  } catch (error) {
    logger.error('Restore error:', error);
    res.status(500).json({ error: 'Failed to restore backup' });
  }
});

// Get list of available backups
adminRouter.get('/backups', (req, res) => {
  try {
    const backupsDir = path.join(process.cwd(), 'backups');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(backupsDir)) {
      fs.mkdirSync(backupsDir);
      return res.json([]);
    }
    
    // Read directory
    const files = fs.readdirSync(backupsDir)
      .filter(file => file.endsWith('.sql'))
      .map(file => {
        const stats = fs.statSync(path.join(backupsDir, file));
        return {
          filename: file,
          size: stats.size,
          created: stats.mtime
        };
      })
      .sort((a, b) => b.created.getTime() - a.created.getTime()); // Sort by date, newest first
    
    res.json(files);
  } catch (error) {
    logger.error('Get backups error:', error);
    res.status(500).json({ error: 'Failed to get backups' });
  }
});

// Get audit logs
adminRouter.get('/audit-logs', async (req, res) => {
  try {
    const { limit = 100, offset = 0, userId, action, resourceType, startDate, endDate } = req.query;
    
    // Build query
    let sql = 'SELECT * FROM audit_logs';
    const params = [];
    const conditions = [];
    
    if (userId) {
      conditions.push('user_id = ?');
      params.push(userId);
    }
    
    if (action) {
      conditions.push('action = ?');
      params.push(action);
    }
    
    if (resourceType) {
      conditions.push('resource_type = ?');
      params.push(resourceType);
    }
    
    if (startDate) {
      conditions.push('timestamp >= ?');
      params.push(startDate);
    }
    
    if (endDate) {
      conditions.push('timestamp <= ?');
      params.push(endDate);
    }
    
    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ');
    }
    
    sql += ' ORDER BY timestamp DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit as string));
    params.push(parseInt(offset as string));
    
    const logs = await query(sql, params) as any[];
    
    // Get total count for pagination
    let countSql = 'SELECT COUNT(*) as count FROM audit_logs';
    if (conditions.length > 0) {
      countSql += ' WHERE ' + conditions.join(' AND ');
    }
    
    const countResult = await query(countSql, params.slice(0, -2)) as any[];
    const totalCount = countResult[0].count;
    
    res.json({
      logs,
      pagination: {
        total: totalCount,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        pages: Math.ceil(totalCount / parseInt(limit as string))
      }
    });
  } catch (error) {
    logger.error('Get audit logs error:', error);
    res.status(500).json({ error: 'Failed to get audit logs' });
  }
});

// Clear local storage (for testing/development)
adminRouter.post('/clear-storage', (req, res) => {
  try {
    // This is a server-side endpoint, so we can't directly clear browser localStorage
    // Instead, we'll return instructions for the client to clear its localStorage
    res.json({
      message: 'To clear localStorage, execute the following in your browser console: localStorage.clear()'
    });
  } catch (error) {
    logger.error('Clear storage error:', error);
    res.status(500).json({ error: 'Failed to clear storage' });
  }
});

// Clear all resource locks (for testing/development)
adminRouter.post('/clear-locks', (req, res) => {
  try {
    // Import the socket handlers to access the lock clearing function
    const { clearAllResourceLocks } = require('../socket/socketHandlers');

    const clearedCount = clearAllResourceLocks();

    logger.info(`Admin manually cleared ${clearedCount} resource locks`);
    res.json({
      message: `Successfully cleared ${clearedCount} resource locks`,
      clearedCount
    });
  } catch (error) {
    logger.error('Clear locks error:', error);
    res.status(500).json({ error: 'Failed to clear resource locks' });
  }
});

// Year Rollover Management Endpoints

// Get year rollover status
adminRouter.get('/year-rollover/status', async (req, res) => {
  try {
    const status = await yearRolloverService.getRolloverStatus();
    res.json(status);
  } catch (error) {
    logger.error('Get rollover status error:', error);
    res.status(500).json({ error: 'Failed to get rollover status' });
  }
});

// Trigger manual year rollover
adminRouter.post('/year-rollover/trigger', async (req, res) => {
  try {
    const { targetYear } = req.body;

    if (!targetYear || !Number.isInteger(targetYear)) {
      return res.status(400).json({ error: 'Valid target year is required' });
    }

    const success = await yearRolloverService.triggerManualRollover(targetYear);

    if (success) {
      res.json({
        success: true,
        message: `Year rollover to ${targetYear} completed successfully`
      });
    } else {
      res.status(500).json({ error: 'Year rollover failed' });
    }
  } catch (error) {
    logger.error('Manual rollover error:', error);
    res.status(500).json({ error: 'Failed to trigger year rollover' });
  }
});

// Backup Scheduler Management Endpoints

// Get backup scheduler status
adminRouter.get('/backup-schedule', (req, res) => {
  try {
    const status = backupScheduler.getStatus();
    res.json({
      ...status,
      message: status.isScheduled ?
        `Daily backup with random timing (${status.randomOffset})` :
        'No backup scheduled'
    });
  } catch (error) {
    logger.error('Get backup schedule error:', error);
    res.status(500).json({ error: 'Failed to get backup schedule' });
  }
});

// Set daily backup schedule
adminRouter.post('/backup-schedule', (req, res) => {
  try {
    const { time, enabled } = req.body;

    if (!enabled) {
      backupScheduler.stop();
      res.json({ message: 'Backup scheduling disabled' });
      return;
    }

    if (!time) {
      return res.status(400).json({ error: 'Time is required when enabling backup schedule' });
    }

    backupScheduler.scheduleDaily(time);

    const status = backupScheduler.getStatus();
    res.json({
      message: 'Backup schedule updated successfully',
      scheduledTime: time,
      nextBackup: status.nextBackup,
      randomOffset: status.randomOffset,
      backupType: status.backupType
    });

  } catch (error) {
    logger.error('Set backup schedule error:', error);
    res.status(500).json({
      error: 'Failed to set backup schedule',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Trigger manual backup
adminRouter.post('/backup-manual', async (req, res) => {
  try {
    const success = await backupScheduler.triggerManualBackup();

    if (success) {
      res.json({ message: 'Manual backup completed successfully' });
    } else {
      res.status(500).json({ error: 'Manual backup failed' });
    }

  } catch (error) {
    logger.error('Manual backup error:', error);
    res.status(500).json({
      error: 'Failed to perform manual backup',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// PRODUCTION FIX: Clear stale department locks
adminRouter.post('/clear-stale-locks', async (req, res) => {
  try {
    const { department } = req.body;

    // Import the department locks management functions
    const { releaseDepartmentLock, departmentLocks } = require('../socket/socketHandlers');

    let clearedLocks = 0;
    const clearedDepartments: string[] = [];

    if (department) {
      // Clear specific department lock
      const lockExists = departmentLocks.has(department);
      if (lockExists) {
        departmentLocks.delete(department);
        clearedLocks = 1;
        clearedDepartments.push(department);
        logger.info(`🔧 ADMIN: Manually cleared stale lock for ${department}`);
      }
    } else {
      // Clear all department locks
      clearedLocks = departmentLocks.size;
      for (const dept of departmentLocks.keys()) {
        clearedDepartments.push(dept);
      }
      departmentLocks.clear();
      logger.info(`🔧 ADMIN: Manually cleared all ${clearedLocks} stale locks`);
    }

    res.json({
      success: true,
      message: `Cleared ${clearedLocks} stale lock(s)`,
      clearedDepartments,
      clearedCount: clearedLocks
    });
  } catch (error) {
    logger.error('Failed to clear stale locks:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});
