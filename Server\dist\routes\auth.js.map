{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/routes/auth.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAA8B;AAC9B,iDAAiD;AACjD,+BAAoC;AACpC,6CAA0C;AAC1C,mDAAqD;AACrD,kDAA4C;AAC5C,mEAA0E;AAC1E,mEAA4D;AAG/C,QAAA,UAAU,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAE3C,8CAA8C;AAC9C,kBAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC3C,IAAI,CAAC;QACH,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEpD,mBAAmB;QACnB,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,iDAAiD;aACzD,CAAC,CAAC;QACL,CAAC;QAED,mDAAmD;QACnD,MAAM,oBAAoB,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAC7D,MAAM,kBAAkB,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAEzD,gEAAgE;QAChE,MAAM,KAAK,GAAG,MAAM,IAAA,aAAK,EAAC;;;;KAIzB,EAAE,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAQ3C,CAAC;QAEH,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,kBAAM,CAAC,IAAI,CAAC,0CAA0C,kBAAkB,KAAK,oBAAoB,GAAG,CAAC,CAAC;YACtG,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,0EAA0E;aAClF,CAAC,CAAC;QACL,CAAC;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEtB,+DAA+D;QAC/D,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC/B,kBAAM,CAAC,IAAI,CAAC,0CAA0C,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACxF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,0EAA0E;aAClF,CAAC,CAAC;QACL,CAAC;QAED,8BAA8B;QAC9B,MAAM,IAAA,aAAK,EAAC,kDAAkD,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3E,kBAAM,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,aAAa,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAE1F,sCAAsC;QACtC,IAAI,CAAC;YACH,MAAM,+BAAY,CAAC,QAAQ,CACzB,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,UAAU,EACf,GAAG,CAAC,EAAE,IAAI,SAAS,EACnB,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS,CACnC,CAAC;QACJ,CAAC;QAAC,OAAO,UAAU,EAAE,CAAC;YACpB,kBAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,UAAU,CAAC,CAAC;YACtD,0CAA0C;QAC5C,CAAC;QAED,8DAA8D;QAC9D,sEAAsE;QACtE,MAAM,IAAA,aAAK,EACT,uGAAuG,CACxG,CAAC;QAEF,mEAAmE;QACnE,oEAAoE;QACpE,MAAM,SAAS,GAAG,IAAA,SAAM,GAAE,CAAC;QAE3B,MAAM,IAAA,aAAK,EACT;+CACyC,EACzC,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CACjD,CAAC;QAEF,kBAAM,CAAC,IAAI,CAAC,gCAAgC,IAAI,CAAC,IAAI,cAAc,SAAS,GAAG,CAAC,CAAC;QAEjF,qEAAqE;QACrE,MAAM,IAAA,aAAK,EACT;mGAC6F,EAC7F,CAAC,IAAI,CAAC,EAAE,CAAC,CACV,CAAC;QAEF,qEAAqE;QACrE,GAAG,CAAC,MAAM,CAAC,gBAAgB,EAAE,SAAS,EAAE;YACtC,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE,KAAK,EAAE,eAAe;YAC9B,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW;YACxC,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;QAEH,sDAAsD;QACtD,OAAO,GAAG,CAAC,IAAI,CAAC;YACd,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,SAAS,EAAE,uCAAuC;YAC7D,IAAI,EAAE;gBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;YACD,OAAO,EAAE,mCAAmC;SAC7C,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC3C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,KAAK,EAAE,uDAAuD;SAC/D,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,iBAAiB;AACjB,kBAAU,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9C,IAAI,CAAC;QACH,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEhD,iBAAiB;QACjB,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;YACtC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,6CAA6C,EAAE,CAAC,CAAC;QACxF,CAAC;QAED,oCAAoC;QACpC,MAAM,cAAc,GAAG,IAAA,SAAM,GAAE,CAAC;QAChC,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,oBAAoB,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QAEtD,sDAAsD;QACtD,OAAO,CAAC,GAAG,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC;QAEtD,kEAAkE;QAElE,oBAAoB;QACpB,OAAO,CAAC,GAAG,CAAC,8BAA8B,cAAc,KAAK,oBAAoB,cAAc,cAAc,EAAE,CAAC,CAAC;QAEjH,MAAM,IAAA,aAAK,EACT,0HAA0H,EAC1H,CAAC,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,oBAAoB,EAAE,SAAS,CAAC,CAC5E,CAAC;QAEF,sCAAsC;QACtC,MAAM,cAAc,GAAG,IAAA,SAAM,GAAE,CAAC;QAChC,MAAM,IAAA,aAAK,EACT,0GAA0G,EAC1G,CAAC,cAAc,EAAE,OAAO,EAAE,0BAA0B,cAAc,KAAK,oBAAoB,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,CAChH,CAAC;QAEF,8CAA8C;QAC9C,MAAM,eAAe,GAAG;YACtB,EAAE,EAAE,cAAc;YAClB,IAAI,EAAE,cAAc;YACpB,UAAU,EAAE,oBAAoB;YAChC,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACvC,MAAM,EAAE,SAAS;SAClB,CAAC;QAEF,yDAAyD;QACzD,IAAA,+CAA2B,EAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QAExD,oBAAoB;QACpB,OAAO,CAAC,GAAG,CAAC,kCAAkC,cAAc,EAAE,CAAC,CAAC;QAEhE,oCAAoC;QACpC,MAAM,mBAAmB,GAAG,MAAM,IAAA,aAAK,EAAC,kDAAkD,EAAE,CAAC,cAAc,CAAC,CAAU,CAAC;QACvH,OAAO,CAAC,GAAG,CAAC,uBAAuB,mBAAmB,CAAC,MAAM,0BAA0B,cAAc,EAAE,CAAC,CAAC;QAEzG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,6CAA6C;YACtD,cAAc;SACf,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;IACzD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,yBAAyB;AACzB,kBAAU,CAAC,GAAG,CAAC,KAAK,EAAE,sBAAY,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACrD,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAE3B,yBAAyB;QACzB,MAAM,KAAK,GAAG,MAAM,IAAA,aAAK,EAAC,kCAAkC,EAAE,CAAC,MAAM,CAAC,CAAU,CAAC;QAEjF,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEtB,6CAA6C;QAC7C,GAAG,CAAC,IAAI,CAAC;YACP,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;SAClC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAC/C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,iDAAiD;AACjD,kBAAU,CAAC,IAAI,CAAC,SAAS,EAAE,sBAAY,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC1D,IAAI,CAAC;QACH,kDAAkD;QAClD,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;QAErC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,kBAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,kDAAkD,CAAC,CAAC;YACrG,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,yEAAyE;QACzE,IAAI,CAAC;YACH,MAAM,EAAE,wBAAwB,EAAE,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;YACzE,MAAM,aAAa,GAAG,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5D,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;gBACtB,kBAAM,CAAC,IAAI,CAAC,+BAA+B,aAAa,4BAA4B,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;YACxH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kBAAM,CAAC,IAAI,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YAClE,0CAA0C;QAC5C,CAAC;QAED,+BAA+B;QAC/B,MAAM,IAAA,aAAK,EACT,gFAAgF,EAChF,CAAC,SAAS,CAAC,CACZ,CAAC;QAEF,4BAA4B;QAC5B,IAAI,CAAC;YACH,MAAM,+BAAY,CAAC,SAAS,CAC1B,GAAG,CAAC,IAAI,CAAC,EAAE,EACX,GAAG,CAAC,IAAI,CAAC,IAAI,EACb,GAAG,CAAC,IAAI,CAAC,UAAU,EACnB,GAAG,CAAC,EAAE,IAAI,SAAS,EACnB,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS,CACnC,CAAC;QACJ,CAAC;QAAC,OAAO,UAAU,EAAE,CAAC;YACpB,kBAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,UAAU,CAAC,CAAC;YACvD,2CAA2C;QAC7C,CAAC;QAED,kBAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,0CAA0C,SAAS,EAAE,CAAC,CAAC;QAExG,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC,CAAC;IACnD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACrC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;IACnD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kEAAkE;AAClE,kBAAU,CAAC,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACtD,IAAI,CAAC;QACH,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAE1D,qEAAqE;QACrE,IAAI,UAAU,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC;YACH,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;gBACnC,UAAU,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YACxC,CAAC;QACH,CAAC;QAAC,OAAO,SAAS,EAAE,CAAC;YACnB,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACxC,CAAC;QAED,kBAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,KAAK,MAAM,QAAQ,UAAU,EAAE,CAAC,CAAC;QAEhF,IAAI,SAAS,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YACzC,8CAA8C;YAC9C,MAAM,YAAY,GAAG,MAAM,IAAA,aAAK,EAAC,sFAAsF,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YAEtI,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,iCAAiC;gBACjC,MAAM,IAAA,aAAK,EAAC,mGAAmG,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;gBACtI,kBAAM,CAAC,IAAI,CAAC,+BAA+B,SAAS,wBAAwB,CAAC,CAAC;YAChF,CAAC;iBAAM,CAAC;gBACN,kBAAM,CAAC,IAAI,CAAC,gCAAgC,SAAS,gCAAgC,CAAC,CAAC;YACzF,CAAC;YAED,iFAAiF;YACjF,IAAI,CAAC;gBACH,MAAM,EAAE,wBAAwB,EAAE,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;gBACzE,MAAM,SAAS,GAAG,MAAM,wBAAwB,CAAC,MAAM,CAAC,CAAC;gBACzD,kBAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,wBAAwB,SAAS,iBAAiB,CAAC,CAAC;YACpG,CAAC;YAAC,OAAO,SAAS,EAAE,CAAC;gBACnB,kBAAM,CAAC,IAAI,CAAC,8CAA8C,EAAE,SAAS,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,kBAAM,CAAC,IAAI,CAAC,6DAA6D,MAAM,EAAE,CAAC,CAAC;YAEnF,+DAA+D;YAC/D,IAAI,CAAC;gBACH,MAAM,EAAE,wBAAwB,EAAE,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;gBACzE,MAAM,SAAS,GAAG,MAAM,wBAAwB,CAAC,MAAM,CAAC,CAAC;gBACzD,kBAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,wCAAwC,SAAS,iBAAiB,CAAC,CAAC;YACpH,CAAC;YAAC,OAAO,SAAS,EAAE,CAAC;gBACnB,kBAAM,CAAC,IAAI,CAAC,8CAA8C,EAAE,SAAS,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,wCAAwC;QACxC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACjD,qDAAqD;QACrD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC1G,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,2DAA2D;AAC3D,kBAAU,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACxD,IAAI,CAAC;QACH,kDAAkD;QAClD,MAAM,KAAK,GAAG,MAAM,IAAA,aAAK,EAAC;;;;KAIzB,CAAU,CAAC;QAEZ,kBAAM,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC,MAAM,2BAA2B,CAAC,CAAC;QAEhE,+CAA+C;QAC/C,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,qCAAqC,CAAC,CAAC;QACtE,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACpC,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAE9B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACtD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;IACzD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,8EAA8E;AAC9E,+DAA+D"}