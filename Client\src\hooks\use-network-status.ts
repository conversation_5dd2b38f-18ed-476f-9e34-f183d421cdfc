import { useEffect } from 'react';
import { useAppStore } from '@/lib/store';

/**
 * Network Status Hook
 * 
 * Monitors network connectivity and automatically updates offline store
 * Handles online/offline events and triggers auto-sync when connection restored
 */
export function useNetworkStatus() {
  const setOnlineStatus = useAppStore(state => state.setOnlineStatus);
  const isOnline = useAppStore(state => state.isOnline);
  const syncOfflineOperations = useAppStore(state => state.syncOfflineOperations);

  useEffect(() => {
    // Set initial online status
    setOnlineStatus(navigator.onLine);

    // Handle online event
    const handleOnline = () => {
      console.log('🌐 Network: Online event detected');
      setOnlineStatus(true);
    };

    // Handle offline event
    const handleOffline = () => {
      console.log('📴 Network: Offline event detected');
      setOnlineStatus(false);
    };

    // Enhanced network detection with server ping
    const checkServerConnection = async () => {
      if (!navigator.onLine) {
        setOnlineStatus(false);
        return;
      }

      try {
        // Ping server health endpoint
        const response = await fetch('/api/health', {
          method: 'HEAD',
          cache: 'no-cache',
          signal: AbortSignal.timeout(5000) // 5 second timeout
        });

        if (response.ok) {
          if (!isOnline) {
            console.log('🌐 Server connection restored');
            setOnlineStatus(true);
          }
        } else {
          console.log('⚠️ Server not responding');
          setOnlineStatus(false);
        }
      } catch (error) {
        console.log('❌ Server connection failed:', error.message);
        setOnlineStatus(false);
      }
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // AUTOMATED: Check server connection aggressively (every 5 seconds)
    const connectionCheckInterval = setInterval(checkServerConnection, 5000);

    // Initial server check
    checkServerConnection();

    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(connectionCheckInterval);
    };
  }, [setOnlineStatus, isOnline]);

  // Manual sync trigger
  const triggerSync = async () => {
    if (isOnline) {
      await syncOfflineOperations();
    }
  };

  return {
    isOnline,
    triggerSync
  };
}
